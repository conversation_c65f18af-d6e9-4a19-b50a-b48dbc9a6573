import { call, put, takeEvery } from 'redux-saga/effects';
import { PayloadAction } from '@reduxjs/toolkit';
import { actions } from './chatbot.slice';
import axios from 'axios';

function* handleSendMessage(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    // Set loading state
    yield put(actions.setLoading(true));
    
    // Get user message from action payload
    const message = action.payload;
    
    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      yield put(actions.setError('Authentication required'));
      return;
    }
    
    // Call the Next.js API route instead of StrapiClient directly
    const response = yield call(axios.post, '/api/chatbot', 
      { message },
      { 
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log(response);
    
    // Handle response
    if (response && response.data) {
      // Remove sessionId storage
      
      // Add AI response to messages
      yield put(actions.setMessage({ 
        type: 'ai', 
        content: response.data.botMessage.content 
      }));
    } else {
      // Handle unexpected response format
      yield put(actions.setError("Received an invalid response format from the server"));
    }
  } catch (error: any) {
    yield put(actions.setError("Failed to send message"));
  } finally {
    yield put(actions.setLoading(false));
  }
}

function* handleEndSession(): Generator<any, void, any> {
  try {
    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      console.log("No authentication token available");
      return;
    }
    
    // Call the endpoint for ending sessions without sessionId
    yield call(axios.post, '/api/chatbot/end', 
      {}, // Empty body, no sessionId needed
      { 
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log("Chat session ended successfully");
  } catch (error: any) {
    console.error("Error ending chat session:", error);
  }
}

export default function* chatbotSaga() {
  yield takeEvery(actions.sendMessage.type, handleSendMessage);
  yield takeEvery(actions.endSession.type, handleEndSession);
}

