import { call, put, takeLatest } from "redux-saga/effects";
import { actions } from "./auth.slice";
import { PayloadAction } from "@reduxjs/toolkit";
import FirebaseAuthService from "@/utils/firebase";
import axios from "axios";
import { getRedirectUrlAfterAuth } from "@/utils/authRedirect";

// Safe browser check
const isBrowser = typeof window !== "undefined";

// API request utility function for auth middleware
const apiAuthRequest = async (action: string, data: any) => {
  const response = await axios.post("/api/auth", {
    action,
    ...data,
  });

  console.log("LOG-response", response);
  return response.data;
};

// Handle email/password sign-in
function* handleSignIn(
  action: PayloadAction<{ email: string; password: string }>
): Generator<any, void, any> {
  try {
    yield put(actions.setLoading(true));

    const { email, password } = action.payload;

    // Add some debugging
    console.log("Attempting sign-in with:", {
      email,
      passwordLength: password.length,
    });

    // Use API middleware instead of direct Strapi call
    const authResponse = yield call(apiAuthRequest, "signin", {
      email,
      password,
    });
    console.log("Sign-in response:", authResponse);

    if (authResponse?.jwt && authResponse?.user) {
      // Set authenticated user in the store
      yield put(
        actions.setAuthenticatedUser({
          user: authResponse.user,
          jwt: authResponse.jwt,
        })
      );

      // Show success toast via action
      yield put(actions.setAuthSuccess("Successfully signed in"));

      // Get redirect URL with fallback to homepage
      const redirectUrl = getRedirectUrlAfterAuth();

      // Redirect after successful login
      if (isBrowser) {
        window.location.href = redirectUrl;
      }
    } else {
      // If we got a response but missing jwt or user
      console.error("Invalid auth response structure:", authResponse);
      yield put(actions.setError("Invalid authentication response received"));
    }
  } catch (error: any) {
    console.error("Sign-in error Saga:", error);

    // Better error message extraction
    let errorMsg = "Sign-in failed. Please check your credentials.";
    if (error instanceof Error) {
      errorMsg = error.message;
    } else if (typeof error === "string") {
      errorMsg = error;
    } else if (error && error.message) {
      errorMsg = error.message;
    }

    yield put(actions.setError(errorMsg));
  } finally {
    yield put(actions.setLoading(false));
  }
}

// Handle user registration
function* handleSignUp(
  action: PayloadAction<{ fullName: string; email: string; password: string }>
): Generator<any, void, any> {
  try {
    yield put(actions.setLoading(true));

    const userData = action.payload;

    // Use API middleware instead of direct Strapi call
    const signupResponse = yield call(apiAuthRequest, "signup", userData);

    console.log("Sign-up response:", signupResponse);

    if (signupResponse?.user) {
      // If we have JWT, set the authenticated user
      const successMessage =
        signupResponse.message ||
        "Account created successfully. Please verify your email.";
      yield put(actions.setAuthSuccess(successMessage));

      // Redirect to verification page or login page
      if (isBrowser) {
        setTimeout(() => {
          window.location.href = "/authentication";
        }, 3000); // Redirect after 3 seconds
      }
    } else {
      yield put(actions.setError("Invalid registration response received"));
    }
  } catch (error: any) {
    console.error("Sign-up error:", error);
    const errorMsg = error.message || "Registration failed. Please try again.";
    yield put(actions.setError(errorMsg));
  } finally {
    yield put(actions.setLoading(false));
  }
}

// New Firebase Google sign in handler
function* handleFirebaseGoogleSignIn(): Generator<any, void, any> {
  try {
    console.log("Starting Firebase Google sign-in process");

    // Call Firebase auth service to sign in with Google popup
    const result = yield call(FirebaseAuthService.signInWithGoogle);

    if (!result) {
      // User might have closed the popup without completing auth
      console.log("Firebase auth result is null - user cancelled");
      yield put(actions.setError("Authentication was cancelled"));
      return;
    }

    const { idToken } = result;
    console.log("Firebase ID Token received, length:", idToken?.length);

    if (!idToken) {
      yield put(
        actions.setError("Failed to get authentication token from Firebase")
      );
      return;
    }

    // Use API middleware instead of direct Strapi call
    const authResponse = yield call(apiAuthRequest, "firebase-auth", {
      idToken,
    });

    console.log("Firebase auth response:", authResponse);

    if (authResponse?.jwt && authResponse?.user) {
      // Set authenticated user in the store
      yield put(
        actions.setAuthenticatedUser({
          user: authResponse.user,
          jwt: authResponse.jwt,
        })
      );

      // Show success toast via action
      yield put(actions.setAuthSuccess("Successfully signed in"));

      // Get redirect URL with fallback to homepage
      const redirectUrl = getRedirectUrlAfterAuth();

      console.log("Redirecting to:", redirectUrl);

      // Redirect after successful login
      if (isBrowser) {
        // Use a small delay to ensure state is updated
        setTimeout(() => {
          window.location.href = redirectUrl;
        }, 100);
      }
    } else {
      console.error("Invalid auth response structure:", authResponse);
      yield put(actions.setError("Invalid authentication response received"));
    }
  } catch (error: any) {
    console.error("Firebase Google sign in error:", error);

    let errorMessage = "Failed to sign in with Google";

    // Handle specific Firebase errors
    if (error.code === "auth/popup-closed-by-user") {
      errorMessage = "Sign-in popup was closed before completing the process";
    } else if (error.code === "auth/popup-blocked") {
      errorMessage = "Sign-in popup was blocked by your browser";
    } else if (error.code === "auth/cancelled-popup-request") {
      errorMessage = "Sign-in process was cancelled";
    } else if (error.code === "auth/account-exists-with-different-credential") {
      errorMessage =
        "An account already exists with the same email address but different sign-in credentials";
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    yield put(actions.setError(errorMessage));
  }
  // Note: socialLoginLoading will be reset by setError or setAuthenticatedUser actions
}

// Handle Google Auth URL request
function* handleGetGoogleAuthUrl(): Generator<any, void, any> {
  try {
    yield put(actions.setLoading(true));

    // Use API middleware instead of direct Strapi call
    const response = yield call(apiAuthRequest, "google-auth-url", {});

    if (response.googleAuthUrl) {
      yield put(actions.setGoogleAuthUrl(response.googleAuthUrl));

      // Redirect to Google Auth URL (only in browser)
      if (isBrowser) {
        // The backend will redirect back to our callback page after Google auth
        window.location.href = response.googleAuthUrl;
      }
    } else {
      const errorMsg = "Invalid response from Google authentication service";
      yield put(actions.setError(errorMsg));
    }
  } catch (error: any) {
    console.error("Failed to get Google Auth URL:", error);
    const errorMsg = error.message || "Failed to get Google authentication URL";
    yield put(actions.setError(errorMsg));
  } finally {
    yield put(actions.setLoading(false));
  }
}

// Handle auth token received from backend callback
function* handleAuthToken(
  action: PayloadAction<{
    token?: string;
    user?: any;
    error?: string;
    redirectUrl?: string;
  }>
): Generator<any, void, any> {
  try {
    yield put(actions.setLoading(true));
    const { token, user, error, redirectUrl } = action.payload;
    console.log("LOG-{ token, user, error, redirectUrl }", {
      token,
      user,
      error,
      redirectUrl,
    });

    if (error) {
      yield put(actions.setError(error));
      return;
    }

    if (token) {
      // Set authenticated user in the store
      yield put(
        actions.setAuthenticatedUser({
          user: user,
          jwt: token,
        })
      );

      // Redirect after successful login:
      // 1. Use redirectUrl from payload if provided
      // 2. Otherwise, check for redirect param in URL
      // 3. Fall back to homepage
      const finalRedirectUrl =
        action.payload.redirectUrl || getRedirectUrlAfterAuth();

      if (isBrowser) {
        window.location.href = finalRedirectUrl;
      }
    } else {
      const errorMsg = "Invalid authentication data received";
      yield put(actions.setError(errorMsg));
    }
  } catch (error: any) {
    console.error("Authentication error:", error);
    const errorMsg = error.message || "Authentication failed";
    yield put(actions.setError(errorMsg));
  } finally {
    yield put(actions.setLoading(false));
  }
}

// Check authentication status
function* handleCheckAuthStatus(): Generator<any, void, any> {
  // The slice handles getting data from localStorage directly
  yield put(actions.checkAuthStatus());
}

// Handle logout - the slice will handle localStorage cleanup
function* handleSignOut(): Generator<any, void, any> {
  console.log("Handling user logout");
  try {
    // Add Firebase signout
    if (isBrowser) {
      try {
        yield call(FirebaseAuthService.signOut);
      } catch (error: any) {
        console.error("Firebase sign out error:", error);
        // Don't throw error for Firebase signout failures, just log them
      }
    }

    // Clear auth data from store and localStorage
    // yield put(actions.logout());

    // Only redirect if not already on the authentication page
    if (isBrowser) {
      const currentPath = window.location.pathname;
      if (currentPath !== "/authentication") {
        console.log("Redirecting to authentication page from:", currentPath);
        window.location.href = "/authentication";
      } else {
        console.log("Already on authentication page, skipping redirect");
      }
    }
  } catch (error: any) {
    console.error("Logout error:", error);
    // Even if there's an error, still clear auth data
    // yield put(actions.logout());

    // Still attempt redirect on error, but only if not on auth page
    if (isBrowser && window.location.pathname !== "/authentication") {
      window.location.href = "/authentication";
    }
  }
}

export default function* authSaga() {
  yield takeLatest(actions.getGoogleAuthUrl.type, handleGetGoogleAuthUrl);
  yield takeLatest(actions.handleAuthToken.type, handleAuthToken);
  yield takeLatest(actions.checkAuthStatus.type, handleCheckAuthStatus);
  yield takeLatest(actions.logout.type, handleSignOut);
  // Add new Firebase Google sign in action
  yield takeLatest("auth/firebaseGoogleSignIn", handleFirebaseGoogleSignIn);
  // Add email/password authentication actions
  yield takeLatest("auth/signIn", handleSignIn);
  yield takeLatest("auth/signUp", handleSignUp);
}
