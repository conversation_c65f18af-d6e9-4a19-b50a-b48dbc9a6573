import { call, put, takeEvery } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import axios from "axios";
import { actions } from "./referral-commission.slice";

function* fetchCommissionsSaga(
  action: PayloadAction<{ page?: number; pageSize?: number; isAdmin?: boolean }>
): Generator<any, void, any> {
  try {
    const { page = 1, pageSize = 10, isAdmin = false } = action.payload;

    // Get appropriate token based on admin status
    const token =
      typeof window !== "undefined"
        ? localStorage.getItem(isAdmin ? "admin_token" : "auth_token")
        : null;

    if (!token) {
      throw new Error("Authentication required");
    }

    // Use appropriate API endpoint
    const apiUrl = isAdmin
      ? "/api/admin/referral-commissions"
      : "/api/referral-commissions";

    const response = yield call(axios.get, apiUrl, {
      params: {
        page,
        pageSize,
      },
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    yield put(actions.fetchCommissionsSuccess(response.data));
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.message ||
      error.message ||
      "Failed to fetch commissions";
    yield put(actions.fetchCommissionsFailure(errorMessage));
  }
}

function* fetchStatsSaga(): Generator<any, void, any> {
  try {
    // Get token from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    if (!token) {
      throw new Error("Authentication required");
    }

    // Use proxy API endpoint
    const response = yield call(axios.get, "/api/referral-commissions/stats", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    yield put(actions.fetchStatsSuccess(response.data));
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.message ||
      error.message ||
      "Failed to fetch commission stats";
    yield put(actions.fetchStatsFailure(errorMessage));
  }
}

export default function* referralCommissionSaga() {
  yield takeEvery(actions.fetchCommissionsRequest.type, fetchCommissionsSaga);
  yield takeEvery(actions.fetchStatsRequest.type, fetchStatsSaga);
}
