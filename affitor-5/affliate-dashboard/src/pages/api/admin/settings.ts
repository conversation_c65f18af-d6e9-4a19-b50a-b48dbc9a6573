import { AppError } from "@/interfaces";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";
import { StrapiAdminClient } from "@/utils/request";
import type { NextApiRequest, NextApiResponse } from "next";

interface ProgramSettings {
  payoutCycle: string;
  minimumPayout: number;
  processingFee: number;
  reserveRate: number;
  cookieDuration: number;
  paymentMethods: {
    paypal: boolean;
    bankTransfer: boolean;
  };
}

// Strapi commission config interface
interface StrapiCommissionConfig {
  payout_cycle: string;
  minimum_payout: number;
  processing_fee: number;
  reverse_rate: number;
  cookie_duration: number;
}

// Default settings
const defaultSettings: ProgramSettings = {
  payoutCycle: "biweekly",
  minimumPayout: 50,
  processingFee: 2.50,
  reserveRate: 10,
  cookieDuration: 60,
  paymentMethods: {
    paypal: true,
    bankTransfer: true,
  },
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ProgramSettings | AppError | any>
) {
  const { method } = req;

  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    if (method === "GET") {
      return handleGetSettings(req, res, token!);
    } else if (method === "PUT") {
      return handleUpdateSettings(req, res, token!);
    } else {
      return res.status(405).json({
        error: "Method not allowed",
        statusCode: 405,
      });
    }
  } catch (error: any) {
    console.error("Error in /api/admin/settings:", error);
    sendApiError(res, error, "Error handling admin settings");
  }
}

async function handleGetSettings(
  req: NextApiRequest,
  res: NextApiResponse,
  token: string
) {
  try {
    // Fetch commission config from Strapi using StrapiAdminClient
    const strapiResponse: any = await StrapiAdminClient.getCommissionConfig(token);

    // Handle different possible response structures from Strapi admin API
    let commissionConfig = null;

    // Option 1: strapiResponse.data.attributes (Strapi v4 single-type format)
    if (strapiResponse?.data?.attributes) {
      commissionConfig = strapiResponse.data.attributes;
    }
    // Option 2: strapiResponse.data (direct data - this is what we're getting)
    else if (strapiResponse?.data && typeof strapiResponse.data === 'object' && !Array.isArray(strapiResponse.data)) {
      commissionConfig = strapiResponse.data;
    }
    // Option 3: strapiResponse directly (if response is the config itself)
    else if (strapiResponse && typeof strapiResponse === 'object' && !strapiResponse.data) {
      commissionConfig = strapiResponse;
    }

    if (!commissionConfig) {
      console.log("⚠️ No commission config found, attempting to create default config...");

      try {
        // Try to create a default commission config in Strapi
        const defaultStrapiConfig = {
          payout_cycle: defaultSettings.payoutCycle,
          minimum_payout: defaultSettings.minimumPayout,
          processing_fee: defaultSettings.processingFee,
          reverse_rate: defaultSettings.reserveRate,
          cookie_duration: defaultSettings.cookieDuration,
        };

        console.log("🔧 Creating default config:", defaultStrapiConfig);
        await StrapiAdminClient.updateCommissionConfig(defaultStrapiConfig, token);
        console.log("✅ Default config created successfully");

        // Return the default settings we just created
        return res.status(200).json(defaultSettings);
      } catch (createError: any) {
        console.error("❌ Failed to create default config:", createError);
        console.log("🔄 Returning default settings without creating config");
        return res.status(200).json(defaultSettings);
      }
    }

    // Log individual field values
    console.log("🔧 Field mapping:", {
      payout_cycle: commissionConfig.payout_cycle,
      minimum_payout: commissionConfig.minimum_payout,
      processing_fee: commissionConfig.processing_fee,
      reverse_rate: commissionConfig.reverse_rate,
      cookie_duration: commissionConfig.cookie_duration
    });

    // Transform Strapi data to our frontend format
    const settings: ProgramSettings = {
      payoutCycle: commissionConfig.payout_cycle || defaultSettings.payoutCycle,
      minimumPayout: commissionConfig.minimum_payout || defaultSettings.minimumPayout,
      processingFee: commissionConfig.processing_fee || defaultSettings.processingFee,
      reserveRate: commissionConfig.reverse_rate || defaultSettings.reserveRate,
      cookieDuration: commissionConfig.cookie_duration || defaultSettings.cookieDuration,
      paymentMethods: defaultSettings.paymentMethods, // Not stored in Strapi config
    };

    console.log("✅ Final transformed settings:", settings);
    return res.status(200).json(settings);
  } catch (error: any) {
    console.error("❌ Error fetching admin settings:", error);
    console.error("❌ Error details:", {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });
    // Return default settings on error
    console.log("🔄 Returning default settings due to error");
    return res.status(200).json(defaultSettings);
  }
}

async function handleUpdateSettings(
  req: NextApiRequest,
  res: NextApiResponse,
  token: string
) {
  try {
    const settings: ProgramSettings = req.body;

    // Validate required fields
    if (!settings.payoutCycle || typeof settings.minimumPayout !== 'number') {
      return res.status(400).json({
        error: "Missing required fields",
        statusCode: 400,
      });
    }

    // Validate payout cycle
    const validPayoutCycles = ["weekly", "biweekly", "monthly"];
    if (!validPayoutCycles.includes(settings.payoutCycle)) {
      return res.status(400).json({
        error: "Invalid payout cycle",
        statusCode: 400,
      });
    }

    // Validate numeric fields
    if (settings.minimumPayout < 0) {
      return res.status(400).json({
        error: "Minimum payout must be positive",
        statusCode: 400,
      });
    }

    if (settings.processingFee < 0) {
      return res.status(400).json({
        error: "Processing fee must be positive",
        statusCode: 400,
      });
    }

    if (settings.reserveRate < 0 || settings.reserveRate > 100) {
      return res.status(400).json({
        error: "Reserve rate must be between 0 and 100",
        statusCode: 400,
      });
    }

    if (settings.cookieDuration < 1) {
      return res.status(400).json({
        error: "Cookie duration must be at least 1 day",
        statusCode: 400,
      });
    }

    // Validate payment methods
    if (!settings.paymentMethods.paypal && !settings.paymentMethods.bankTransfer) {
      return res.status(400).json({
        error: "At least one payment method must be enabled",
        statusCode: 400,
      });
    }

    // Transform frontend data to Strapi format
    const strapiData: StrapiCommissionConfig = {
      payout_cycle: settings.payoutCycle,
      minimum_payout: settings.minimumPayout,
      processing_fee: settings.processingFee,
      reverse_rate: settings.reserveRate,
      cookie_duration: settings.cookieDuration,
    };

    // Update commission config in Strapi using StrapiAdminClient
    await StrapiAdminClient.updateCommissionConfig(strapiData, token);

    return res.status(200).json(settings);
  } catch (error: any) {
    console.error("Error updating admin settings:", error);
    return res.status(500).json({
      error: "Internal server error",
      statusCode: 500,
    });
  }
}
