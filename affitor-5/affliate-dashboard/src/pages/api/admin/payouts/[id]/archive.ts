import { NextApiRequest, NextApiResponse } from "next";
import { StrapiAdminClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { method } = req;

  if (method !== "PUT") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    // Extract payout documentId from query
    const { id } = req.query;

    if (!id || typeof id !== "string") {
      return res.status(400).json({ error: "Invalid payout document ID" });
    }

    const documentId = id;

    // Call StrapiAdminClient to archive payout
    const data = await StrapiAdminClient.archivePayout(documentId, token!);

    res.status(200).json(data);
  } catch (error: any) {
    console.error("Archive payout API error:", error);
    sendApiError(res, error, "Error archiving payout");
  }
}
