import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // This endpoint can be used for admin-related operations
    res.status(200).json({
      message: "Admin API endpoint",
      timestamp: new Date().toISOString(),
    });
  } catch (error: any) {
    console.error("Admin API error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
}
