import type { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST method
  if (req.method !== "POST") {
    return res
      .status(405)
      .json({ statusCode: 405, message: "Method not allowed" });
  }

  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    // Extract message from request body
    const { message } = req.body;

    if (!message) {
      return res
        .status(400)
        .json({ statusCode: 400, message: "Bad request - Missing message" });
    }

    try {
      // Use StrapiClient to forward the request to backend
      const response = await StrapiClient.sendMessage(message, token!);

      // Return the response from the backend
      return res.status(200).json(response.data);
    } catch (apiError: any) {
      console.error("API Client error:", apiError);
      return sendApiError(res, apiError, "Failed to process message");
    }
  } catch (error: any) {
    console.error("Chatbot API error:", error);
    return sendApiError(
      res,
      error,
      "An error occurred while processing your request"
    );
  }
}
