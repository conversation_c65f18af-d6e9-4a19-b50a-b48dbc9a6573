// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import { AppError, ICategory } from "@/interfaces";
import { StrapiClient } from "@/utils/request";
import type { NextApiRequest, NextApiResponse } from "next";
import { sendApiError } from "@/utils/api-error-handler";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ICategory[] | AppError>
) {
  try {
    const response: any = await StrapiClient.getCategories();
    res.status(200).json(response);
  } catch (error: any) {
    console.log(error);
    return sendApiError(res, error, "Error fetching categories");
  }
}
