import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const { token } = createApiContext(req, { requireAuth: true });

    // Forward the request to Strapi backend
    const response = await StrapiClient.getAvailablePagesForReferrerLink(token!);

    return res.status(200).json(response);
  } catch (error: any) {
    console.error("Error fetching available pages for referrer link:", error);
    sendApiError(res, error, "Error fetching available pages");
  }
}
