import { Affiliates } from "@/containers";
import { useRouter } from "next/router";
import { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { categoryActions } from "@/features/rootActions";
import { actions as referrerLinksActions } from "@/features/referrer-links/referrer-links.slice";

export default function DynamicCategoryPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const { industry } = router.query;
  const [shortLinkChecked, setShortLinkChecked] = useState(false);
  const checkedPathRef = useRef<string | null>(null);

  // Wait for router to be ready and get the actual industry parameter
  const categoryPath =
    router.isReady && typeof industry === "string" ? industry : null;

  console.log("router.query:", router.query);
  console.log("categoryPath:", categoryPath);
  console.log("router.isReady:", router.isReady);

  // Ensure industry is a string for backward compatibility
  const industryPath = typeof industry === "string" ? industry : "product";

  // Get categories from Redux store
  const categories = useSelector((state: any) => state.category.list);
  const categoriesLoading = useSelector((state: any) => state.category.loading);

  // Get referrer links state
  const referrerLinksError = useSelector(
    (state: any) => state.referrerLinks.error
  );
  const referrerLinksLoading = useSelector(
    (state: any) => state.referrerLinks.loading
  );

  // Track if this is the initial page load vs internal navigation
  const isInitialLoadRef = useRef(true);

  // Handle categoryPath changes and short link checking in a single effect
  useEffect(() => {
    // If categoryPath changed, reset the checked state only for initial loads or external navigation
    if (checkedPathRef.current !== categoryPath) {
      checkedPathRef.current = categoryPath;

      // Only reset shortLinkChecked if this is the initial load
      // Internal category filtering should not trigger short link checking
      if (isInitialLoadRef.current) {
        setShortLinkChecked(false);
        dispatch(referrerLinksActions.clearError());
      }
    }

    // Only check short link if router is ready, this is initial load, and we haven't checked yet for this path
    if (
      router.isReady &&
      categoryPath &&
      categoryPath !== "[industry]" && // Ensure we don't have the literal route pattern
      !shortLinkChecked &&
      isInitialLoadRef.current
    ) {
      console.log(
        `Checking if "${categoryPath}" is a short link (priority check)...`
      );
      dispatch(
        referrerLinksActions.fetchShortLink({ shortCode: categoryPath })
      );
      setShortLinkChecked(true);
    }

    // Mark that initial load is complete after first effect run
    if (isInitialLoadRef.current && router.isReady) {
      isInitialLoadRef.current = false;
    }
  }, [router.isReady, categoryPath, dispatch]);

  // Handle short link error - check if it's a category instead
  useEffect(() => {
    if (
      shortLinkChecked &&
      referrerLinksError === "SHORT_LINK_NOT_FOUND" &&
      categories.length > 0 &&
      !categoriesLoading &&
      categoryPath
    ) {
      // Short link not found, check if it's a category
      const matchingCategory = categories.find(
        (cat: any) => cat.slug === categoryPath
      );

      if (matchingCategory) {
        console.log(
          `"${categoryPath}" is not a short link, but matches category: ${matchingCategory.name}`
        );
        // Clear the error since we found a matching category
        dispatch(referrerLinksActions.clearError());
      } else {
        console.log(
          `"${categoryPath}" is neither a short link nor a category, redirecting to 404`
        );
        // Redirect to 404 page
        router.push("/404");
      }
    }
  }, [
    shortLinkChecked,
    referrerLinksError,
    categories.length,
    categoriesLoading,
    categoryPath,
    router,
    dispatch,
  ]);

  // Load categories if not loaded yet
  useEffect(() => {
    if (categories.length === 0 && !categoriesLoading) {
      console.log("Loading categories...");
      dispatch(categoryActions.fetchAll());
    }
  }, [dispatch, categories.length, categoriesLoading]);

  console.log(`Viewing affiliates for industry: ${industryPath}`);

  // Don't render the component until router is ready
  if (!router.isReady) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 mt-2">Loading...</p>
        </div>
      </div>
    );
  }

  // Show loading while checking short link or processing redirect (only during initial load)
  if (
    (!shortLinkChecked && categoryPath && isInitialLoadRef.current) ||
    (referrerLinksLoading && categoryPath)
  ) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 mt-2">
            {referrerLinksLoading ? "Redirecting..." : "Checking link..."}
          </p>
        </div>
      </div>
    );
  }

  // Show "Page Not Found" only if short link check failed AND it's not a valid category
  if (
    shortLinkChecked &&
    referrerLinksError === "SHORT_LINK_NOT_FOUND" &&
    categories.length > 0 &&
    categoryPath
  ) {
    const matchingCategory = categories.find(
      (cat: any) => cat.slug === categoryPath
    );

    if (!matchingCategory) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">Page Not Found</h1>
            <p className="text-gray-600">Redirecting...</p>
          </div>
        </div>
      );
    }
  }

  // If we reach here, it means:
  // 1. No categoryPath (homepage) - OK to render
  // 2. categoryPath exists and short link check passed - will redirect
  // 3. categoryPath exists, short link failed, but category exists - OK to render
  // 4. Still waiting for categories to load - OK to render (will show loading in Affiliates)

  return (
    <Affiliates
      industryPath={industryPath}
      categoryPath={categoryPath || undefined}
      shortLinkChecked={shortLinkChecked}
    />
  );
}
