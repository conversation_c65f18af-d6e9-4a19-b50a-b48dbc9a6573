import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { CheckCircle, Target, GemIcon, Crown, Zap } from "lucide-react";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { useSelector } from "react-redux";
import { selectIsAuthenticated } from "@/features/auth/auth.slice";

interface PricingInfo {
  price: number | string;
  period: string;
  billedAs: string;
}

interface SavingsInfo {
  amount: string;
  percent: string;
}

interface PricingCardProps {
  tier: any;
  index: number;
  pricing: PricingInfo;
  billingPeriod: "monthly" | "quarterly" | "yearly";
  subscriptionTiers: any[];
  currentSubscription: any;
  onSubscribe: (tierId: number, paymentMethod: string) => void;
  // isAuthenticated prop removed
}

const PricingCard = ({
  tier,
  index,
  pricing,
  billingPeriod,
  subscriptionTiers,
  currentSubscription,
  onSubscribe,
}: PricingCardProps) => {
  const router = useRouter();
  const isAuthenticated = useSelector(selectIsAuthenticated);

  // Update tier detection for 3 tiers
  const isBasic = index === 0;
  const isPro = index === 1;
  const isPremium = index === 2;

  const [animate, setAnimate] = useState(false);

  // Reset animation when billing period changes
  useEffect(() => {
    setAnimate(false);
    const timer = setTimeout(() => setAnimate(true), 50);
    return () => clearTimeout(timer);
  }, [billingPeriod]);

  // Card entrance animation delay based on index
  const animationDelay = `${index * 100}ms`;

  // Handle plan selection with authentication check
  const handlePlanSelection = () => {
    if (!isAuthenticated) {
      router.replace(
        `/authentication?redirect=${encodeURIComponent(router.asPath)}`
      );
      return;
    }
    onSubscribe(tier.id, "stripe");
  };

  // Get the appropriate icon based on tier
  const getTierIcon = () => {
    if (isBasic) {
      return <Target className="h-4 w-4 text-blue-600 dark:text-blue-400" />;
    } else if (isPro) {
      return <Zap className="h-4 w-4 text-green-600 dark:text-green-400" />;
    } else {
      return <Crown className="h-4 w-4 text-purple-600 dark:text-purple-400" />;
    }
  };

  // Get tier description
  const getTierDescription = () => {
    if (tier.description) return tier.description;

    if (isBasic) {
      return "Good for deal hunters getting started.";
    } else if (isPro) {
      return "Powerful tools for growing affiliate marketers.";
    } else {
      return "The ultimate toolkit for serious affiliate professionals.";
    }
  };

  return (
    <Card
      key={tier.id}
      className={`relative overflow-hidden transition-all duration-500 border rounded-xl flex flex-col h-full max-w-[380px] mx-auto w-full ${
        isPro
          ? "shadow-md md:scale-[1.01] bg-white dark:bg-slate-800 hover:shadow-lg border-green-300/30 dark:border-green-700/40"
          : isPremium
          ? "shadow-[0_0_15px_rgba(0,0,0,0.1)] dark:shadow-[0_0_15px_rgba(59,130,246,0.15)] md:scale-[1.01] bg-white dark:bg-slate-800 hover:shadow-[0_0_20px_rgba(0,0,0,0.15)] dark:hover:shadow-[0_0_20px_rgba(59,130,246,0.25)] border-primary/30 dark:border-primary/40"
          : "dark:bg-slate-900 hover:border-muted-foreground/50 dark:hover:border-muted-foreground/30"
      } ${animate ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0"}`}
      style={{ transitionDelay: animationDelay }}
    >
      <CardHeader className="pb-2 px-4 md:px-5">
        <div className="flex items-center mb-2">
          <div
            className={`flex items-center justify-center w-9 h-9 rounded-full ${
              isBasic
                ? "bg-blue-100 dark:bg-blue-900/40"
                : isPro
                ? "bg-green-100 dark:bg-green-900/40 border-2 border-green-200 dark:border-green-500/50"
                : "bg-purple-100 dark:bg-purple-900/40 border-2 border-purple-200 dark:border-purple-500/50"
            } mr-3`}
          >
            {getTierIcon()}
          </div>
          <CardTitle className="text-lg md:text-xl font-bold">
            {tier.display_name}
          </CardTitle>
        </div>
        <CardDescription className="text-xs md:text-sm font-medium dark:text-slate-300">
          {getTierDescription()}
        </CardDescription>
      </CardHeader>

      <CardContent className="flex-grow pt-0 px-4 md:px-5 flex flex-col overflow-y-auto">
        <div className="mb-3">
          <p className="text-2xl md:text-4xl font-bold flex items-baseline">
            $
            {pricing.price
              ? Number.isInteger(parseFloat(pricing.price.toString()))
                ? parseFloat(pricing.price.toString()).toLocaleString("en-US", {
                    maximumFractionDigits: 0,
                  })
                : parseFloat(pricing.price.toString()).toLocaleString("en-US", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })
              : pricing.price}
            <span className="text-xs md:text-sm text-muted-foreground dark:text-slate-400 ml-1 font-normal">
              /month
            </span>
          </p>
        </div>

        <Separator className="my-2 dark:bg-slate-700" />

        <div className="space-y-2 pt-1 text-sm flex-grow">
          {tier.tier_features && tier.tier_features.length > 0 ? (
            tier.tier_features.map((feature: any, idx: number) => (
              <div key={idx} className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400 mt-0.5 mr-2 flex-shrink-0" />
                <span className="dark:text-slate-300">{feature.item}</span>
              </div>
            ))
          ) : isBasic ? (
            <>
              <div className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400 mt-0.5 mr-2 flex-shrink-0" />
                <span className="dark:text-slate-300">
                  Browse unlimited product listings
                </span>
              </div>
              <div className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400 mt-0.5 mr-2 flex-shrink-0" />
                <span className="dark:text-slate-300">
                  Export and optimize product scripts with AI-powered tools
                </span>
              </div>
              <div className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400 mt-0.5 mr-2 flex-shrink-0" />
                <span className="dark:text-slate-300">
                  <b className="dark:text-white">25</b> actions / month
                </span>
              </div>
              <div className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400 mt-0.5 mr-2 flex-shrink-0" />
                <span className="dark:text-slate-300">
                  Access Affiliate Hunt (50/50 revenue split, slower approval)
                </span>
              </div>
            </>
          ) : isPro ? (
            <>
              <div className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400 mt-0.5 mr-2 flex-shrink-0" />
                <span className="dark:text-slate-300">
                  <b className="dark:text-white">Everything in Basic</b>, plus
                </span>
              </div>
              <div className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400 mt-0.5 mr-2 flex-shrink-0" />
                <span className="dark:text-slate-300">
                  <b className="dark:text-white">500</b> actions / month
                </span>
              </div>
              <div className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400 mt-0.5 mr-2 flex-shrink-0" />
                <span className="dark:text-slate-300">
                  Advanced filtering and search capabilities
                </span>
              </div>
              <div className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400 mt-0.5 mr-2 flex-shrink-0" />
                <span className="dark:text-slate-300">
                  Faster processing, improved revenue split for Affiliate Hunt
                  (60/40)
                </span>
              </div>
              <div className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400 mt-0.5 mr-2 flex-shrink-0" />
                <span className="dark:text-slate-300">
                  Access to community of affiliate marketers
                </span>
              </div>
            </>
          ) : (
            <>
              <div className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400 mt-0.5 mr-2 flex-shrink-0" />
                <span className="dark:text-slate-300">
                  <b className="dark:text-white">Everything in Pro</b>, plus
                </span>
              </div>
              <div className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400 mt-0.5 mr-2 flex-shrink-0" />
                <span className="dark:text-slate-300">
                  <b className="dark:text-white">1200</b> actions with
                  AI-powered tools
                </span>
              </div>
              <div className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400 mt-0.5 mr-2 flex-shrink-0" />
                <span className="dark:text-slate-300">
                  Highest revenue split for Affiliate Hunt (80/20)
                </span>
              </div>
              <div className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400 mt-0.5 mr-2 flex-shrink-0" />
                <span className="dark:text-slate-300">
                  Priority access for all upcoming premium features
                </span>
              </div>
              <div className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400 mt-0.5 mr-2 flex-shrink-0" />
                <span className="dark:text-slate-300">
                  <span className="dark:text-yellow-400 font-medium">
                    VIP Support
                  </span>{" "}
                  with 24-hour response time
                </span>
              </div>
            </>
          )}
        </div>
      </CardContent>

      <CardFooter className="pt-2 px-4 md:px-5 flex flex-col space-y-2 relative pb-6">
        <Button
          className={`w-full py-2 md:py-4 font-medium text-sm transition-all ${
            isBasic
              ? "bg-primary text-primary-foreground hover:bg-primary/90 dark:hover:bg-primary/80 border border-primary-foreground/20"
              : isPro
              ? "bg-primary-foreground text-primary hover:bg-primary-foreground/90 border border-primary dark:bg-slate-700 dark:text-slate-200 dark:border-slate-600 dark:hover:bg-slate-600"
              : "bg-primary-foreground text-primary hover:bg-muted border border-primary dark:bg-blue-950 dark:text-blue-300 dark:border-blue-700 dark:hover:bg-blue-900"
          }`}
          variant={isBasic ? "default" : "outline"}
          disabled={currentSubscription?.name === tier.name}
          onClick={handlePlanSelection}
        >
          {currentSubscription?.name === tier.name
            ? "Current Plan"
            : "Select Plan"}
        </Button>

        {/* Show calculated total price for quarterly or yearly plans with absolute positioning */}
        {(billingPeriod === "quarterly" || billingPeriod === "yearly") &&
          tier.price > 0 && (
            <div className="text-xs text-muted-foreground dark:text-slate-400 absolute bottom-2 left-4 md:left-5">
              <span>
                *$
                {(
                  parseFloat(pricing.price.toString()) *
                  (billingPeriod === "quarterly" ? 3 : 12)
                ).toFixed(2)}
              </span>
            </div>
          )}
      </CardFooter>

      {/* Savings badge for yearly plans */}
      {tier.save_percent > 0 && (
        <div className="absolute -top-1 -right-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300 px-3 py-1 rounded-bl-lg rounded-tr-lg text-xs font-medium shadow-sm border border-green-200 dark:border-green-800">
          Save {tier.save_percent}%
        </div>
      )}
    </Card>
  );
};

export default PricingCard;
