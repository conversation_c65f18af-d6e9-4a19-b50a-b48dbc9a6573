import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import {
  selectComparisonPlans,
  selectComparisonPlansLoading,
  selectComparisonPlansError,
  selectCurrentSubscription,
} from "@/features/selectors";
import { selectIsAuthenticated } from "@/features/auth/auth.slice";
import { subscriptionTierActions } from "@/features/rootActions";
import { AppDispatch } from "@/store";
import {
  Search,
  Wrench,
  BarChart3,
  DollarSign,
  Tag,
  Upload,
  Key,
  Bot,
  ShieldCheck,
  ChevronDown,
  ChevronRight,
  Video,
  Users,
  Info, // Add Info icon
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// Custom Check and X icons with circle backgrounds
const CheckCircleIcon = () => (
  <div className="h-6 w-6 rounded-full bg-green-500 flex items-center justify-center">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="14"
      height="14"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="3"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="text-white"
    >
      <polyline points="20 6 9 17 4 12"></polyline>
    </svg>
  </div>
);

const XCircleIcon = () => (
  <div className="h-6 w-6 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="12"
      height="12"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="3"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="text-gray-400 dark:text-gray-500"
    >
      <line x1="18" y1="6" x2="6" y2="18"></line>
      <line x1="6" y1="6" x2="18" y2="18"></line>
    </svg>
  </div>
);

interface CompareTierProps {
  onGetStarted?: () => void;
  onStartFree?: () => void;
  onGetPro?: () => void;
  isLoading?: boolean;
  billingPeriod?: "monthly" | "quarterly" | "yearly"; // Replace annualSubscription with billingPeriod
  subscriptionTiers?: any[]; // Add subscription tiers data
}

interface FeatureSectionProps {
  title: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  defaultOpen?: boolean;
}

const FeatureSection: React.FC<FeatureSectionProps> = ({
  title,
  icon,
  children,
  defaultOpen = true,
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  return (
    <>
      {/* Section Header */}
      <div className="col-span-12 grid grid-cols-12 gap-0 items-center border-b">
        <div className="col-span-12 sm:col-span-4 flex items-center gap-2 p-3 pl-0 md:pl-3 min-h-[52px]">
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            aria-label={isOpen ? "Collapse section" : "Expand section"}
          >
            {isOpen ? (
              <ChevronDown className="h-4 w-4 text-primary-foreground/80" />
            ) : (
              <ChevronRight className="h-4 w-4 text-primary-foreground/80" />
            )}
          </button>
          {icon}
          <span className="font-medium">{title}</span>
        </div>
        <div className="hidden sm:block sm:col-span-2 p-3 min-h-[52px]"></div>
        <div className="hidden sm:block sm:col-span-3 bg-green-50 dark:bg-green-900/30 p-3 min-h-[52px]"></div>
        <div className="hidden sm:block sm:col-span-3 bg-blue-50 dark:bg-blue-900/30 p-3 min-h-[52px]"></div>
      </div>

      {/* Feature Items - Conditionally shown based on isOpen state */}
      <div
        className={cn(
          "col-span-12 transition-all duration-200 overflow-hidden",
          isOpen ? "block" : "hidden"
        )}
      >
        {children}
      </div>
    </>
  );
};

const CompareTier: React.FC<CompareTierProps> = ({
  onGetStarted,
  onStartFree,
  onGetPro,
  isLoading = false,
  billingPeriod = "yearly",
  subscriptionTiers = [],
}) => {
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const comparisonPlans = useSelector(selectComparisonPlans);
  const loading = useSelector(selectComparisonPlansLoading);
  const error = useSelector(selectComparisonPlansError);
  const currentSubscription = useSelector(selectCurrentSubscription);

  useEffect(() => {
    // Fetch comparison plans data
    dispatch(subscriptionTierActions.fetchComparisonPlans());
  }, [dispatch]);

  // Check if user is already on specific plan - MOVED UP to avoid the "used before declaration" error
  const isOnBasicPlan = currentSubscription?.name
    ?.toLowerCase()
    .includes("basic");
  const isOnProPlan = currentSubscription?.name?.toLowerCase().includes("pro");
  const isOnPremiumPlan = currentSubscription?.name
    ?.toLowerCase()
    .includes("premium");

  // Find tiers based on billing period
  const getTierByNameAndPeriod = (name: string, period: string) => {
    console.log("name", name);
    console.log("period", period);
    console.log("subscriptionTiers", subscriptionTiers);
    return subscriptionTiers.find(
      (tier) =>
        tier.display_name?.toLowerCase().includes(name.toLowerCase()) &&
        (period === "yearly"
          ? tier.stripe_recurring_interval === "year"
          : period === "quarterly"
          ? tier.stripe_recurring_interval === "quarter"
          : tier.stripe_recurring_interval === "month" ||
            !tier.stripe_recurring_interval)
    );
  };

  // Get tiers for current billing period
  const basicTier = getTierByNameAndPeriod("basic", billingPeriod);
  const proTier = getTierByNameAndPeriod("pro", billingPeriod);
  const premiumTier = getTierByNameAndPeriod("premium", billingPeriod);

  // Get monthly tiers for comparison
  const basicMonthlyTier = getTierByNameAndPeriod("basic", "monthly");
  const proMonthlyTier = getTierByNameAndPeriod("pro", "monthly");
  const premiumMonthlyTier = getTierByNameAndPeriod("premium", "monthly");

  // Get discount percentages directly from tier data
  const proSavingsPercent = proTier?.save_percent || 0;
  const premiumSavingsPercent = premiumTier?.save_percent || 0;

  // Format currency with 2 decimal places if needed
  const formatCurrency = (amount: number) => {
    return amount % 1 === 0 ? amount : amount.toFixed(2);
  };

  // Helper function to safely get price with fallback
  const getSafePrice = (tier: any, fallback: number = 0) => {
    return tier?.price ?? fallback;
  };

  // Helper function to format price display
  const formatPriceDisplay = (price: number, period: string) => {
    if (price === 0) {
      return "Contact Sales";
    }
    return `$${formatCurrency(price)}/${period}`;
  };

  // Use direct tier prices - each tier already has the correct price for its period
  // These are NOT monthly prices multiplied by period length
  const proCurrentPeriodPrice = getSafePrice(proTier);
  const premiumCurrentPeriodPrice = getSafePrice(premiumTier);

  // Get monthly prices for "or $X/month" display
  const proMonthlyPrice = getSafePrice(proMonthlyTier);
  const premiumMonthlyPrice = getSafePrice(premiumMonthlyTier);

  // Get period label
  const getPeriodLabel =
    billingPeriod === "yearly"
      ? "year"
      : billingPeriod === "quarterly"
      ? "quarter"
      : "month";
  const getProButtonText = isOnProPlan ? "Current Plan" : `Get Pro`;
  const getPremiumButtonText = isOnPremiumPlan ? "Current Plan" : `Get Premium`;

  // Authentication check handlers
  const handleGetPremium = () => {
    if (!isAuthenticated) {
      router.push(
        `/authentication?redirect=${encodeURIComponent(router.asPath)}`
      );
      return;
    }
    if (onGetStarted) onGetStarted();
  };

  const handleGetPro = () => {
    if (!isAuthenticated) {
      router.push(
        `/authentication?redirect=${encodeURIComponent(router.asPath)}`
      );
      return;
    }
    if (onGetPro) onGetPro();
  };

  const handleStartFree = () => {
    if (!isAuthenticated) {
      router.push(
        `/authentication?redirect=${encodeURIComponent(router.asPath)}`
      );
      return;
    }
    if (onStartFree) onStartFree();
  };

  // Add ref for scrollable container
  const tableRef = React.useRef<HTMLDivElement>(null);

  // Add state to show scroll indicators
  const [showScrollIndicator, setShowScrollIndicator] = useState(false);

  // Check if we need to show scroll indicators on mobile
  useEffect(() => {
    const checkScroll = () => {
      if (tableRef.current) {
        const { scrollWidth, clientWidth } = tableRef.current;
        setShowScrollIndicator(scrollWidth > clientWidth);
      }
    };

    // Check on load and resize
    checkScroll();
    window.addEventListener("resize", checkScroll);
    return () => window.removeEventListener("resize", checkScroll);
  }, []);

  // Get traffic share rates directly from tier data - making sure to use the correct tier for the current billing period
  // Convert to percentage and round to whole number
  const basicTrafficRatePercent = basicTier?.traffic_share_rate
    ? Math.round(basicTier.traffic_share_rate * 100)
    : 0; // No fallback - use 0 if data not available

  const proTrafficRatePercent = proTier?.traffic_share_rate
    ? Math.round(proTier.traffic_share_rate * 100)
    : 0; // No fallback - use 0 if data not available

  const premiumTrafficRatePercent = premiumTier?.traffic_share_rate
    ? Math.round(premiumTier.traffic_share_rate * 100)
    : 0; // No fallback - use 0 if data not available

  // Show loading state if subscription tiers are still loading
  if (loading || subscriptionTiers.length === 0) {
    return (
      <div className="w-full max-w-6xl mx-auto px-2 sm:px-4">
        <h3 className="text-lg font-semibold mb-4">Compare all features</h3>
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">
              Loading pricing information...
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if there's an error or no tiers found
  if (error || (!basicTier && !proTier && !premiumTier)) {
    return (
      <div className="w-full max-w-6xl mx-auto px-2 sm:px-4">
        <h3 className="text-lg font-semibold mb-4">Compare all features</h3>
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-muted-foreground mb-2">
              {error
                ? "Error loading pricing information"
                : "No pricing tiers available for the selected billing period"}
            </p>
            <p className="text-sm text-muted-foreground">
              Please try refreshing the page or contact support if the issue
              persists.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto px-2 sm:px-4">
      <h3 className="text-lg font-semibold mb-4">Compare all features</h3>

      {/* Make the container scrollable horizontally on mobile */}
      <div
        ref={tableRef}
        className="overflow-x-auto pt-4 pb-2 -mx-2 px-2 md:overflow-visible md:pb-0 md:mx-0 md:px-0 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-700 scrollbar-track-transparent"
      >
        <div className="grid grid-cols-12 gap-0 min-w-[600px] md:min-w-0 md:w-auto">
          {/* Sticky header row with correct discount badges */}
          <div className="col-span-12 grid grid-cols-12 gap-0 sticky top-0 z-10 bg-background/95 backdrop-blur-sm shadow-sm">
            <div className="col-span-4 sm:col-span-4 p-3 h-[52px]"></div>
            <div className="col-span-2 sm:col-span-2 text-center font-medium py-3 px-1 flex items-center justify-center h-[52px]">
              Basic
            </div>
            <div className="col-span-3 sm:col-span-3 text-center font-medium bg-green-50 dark:bg-green-900/30 py-3 px-1 flex items-center justify-center h-[52px] relative">
              Pro
              {proSavingsPercent > 0 && (
                <span className="absolute -top-2.5 -right-3 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full border border-green-200 dark:border-green-800 shadow-sm rotate-[15deg] z-20">
                  {proSavingsPercent}% off
                </span>
              )}
            </div>
            <div className="col-span-3 sm:col-span-3 text-center font-medium bg-blue-50 dark:bg-blue-900/30 py-3 px-1 rounded-t-lg flex items-center justify-center h-[52px] relative">
              Premium
              {premiumSavingsPercent > 0 && (
                <span className="absolute -top-2.5 -right-3 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full border border-blue-200 dark:border-blue-800 shadow-sm rotate-[15deg] z-20">
                  {premiumSavingsPercent}% off
                </span>
              )}
            </div>
          </div>

          {/* Tool Access Section */}
          <FeatureSection
            title="Tool Access"
            icon={<Wrench className="h-4 w-4 text-blue-500" />}
          >
            {/* Search affiliate programs */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300">
                Search affiliate programs by keyword
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center px-1 py-3">
                <CheckCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 px-1 py-3">
                <CheckCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 px-1 py-3">
                <CheckCircleIcon />
              </div>
            </div>

            {/* Use Affitor tools */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300">
                Use Affitor tools
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center text-xs text-primary-foreground/80 px-1 py-3">
                25/month
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 text-xs px-1 py-3">
                500/month
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 text-xs px-1 py-3">
                1200/month
              </div>
            </div>

            {/* Advanced Filters */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300">
                Advanced Filters (Organic, Paid)
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center text-xs text-primary-foreground/80 px-1 py-3">
                Basic
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 text-xs px-1 py-3">
                Advanced
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 text-xs px-1 py-3">
                Advanced
              </div>
            </div>
          </FeatureSection>

          {/* Traffic Intelligence Section */}
          <FeatureSection
            title="Traffic Intelligence"
            icon={<BarChart3 className="h-4 w-4 text-blue-500" />}
          >
            {/* Filter viral content */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300">
                Filter viral content by organic views
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center text-xs text-primary-foreground/80 px-1 py-3">
                Basic
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 text-xs px-1 py-3">
                Advanced
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 text-xs px-1 py-3">
                Advanced
              </div>
            </div>

            {/* Spy on paid ads */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300 flex flex-wrap items-center">
                Spy on paid ads
                <span className="ml-1 mt-1 sm:mt-0 sm:ml-2 text-xs bg-purple-100 text-purple-800 px-1 rounded">
                  Soon
                </span>
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center px-1 py-3">
                <XCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 px-1 py-3">
                <CheckCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 px-1 py-3">
                <CheckCircleIcon />
              </div>
            </div>

            {/* Competitor sales page insights */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300">
                Competitor sales page insights
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center px-1 py-3">
                <XCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 text-xs px-1 py-3">
                <CheckCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 px-1 py-3">
                <CheckCircleIcon />
              </div>
            </div>

            {/* Discover trending tools & traffic sources */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300">
                Discover trending tools & traffic sources
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center px-1 py-3">
                <XCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 text-xs px-1 py-3">
                TikTok, YouTube & Paid Ads
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 text-xs px-1 py-3">
                TikTok, YouTube & Paid Ads
              </div>
            </div>
          </FeatureSection>

          {/* Affiliate Earnings Section */}
          <FeatureSection
            title="Affiliate Earnings"
            icon={<DollarSign className="h-4 w-4 text-blue-500" />}
          >
            {/* Promote Affitor for commission */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300 flex items-center">
                Promote Affitor for commission
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center text-xs text-primary-foreground/80 px-1 py-3">
                25%
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 text-xs px-1 py-3">
                35%
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 text-xs px-1 py-3">
                50%
              </div>
            </div>
          </FeatureSection>

          {/* Exclusive Deals Section */}
          <FeatureSection
            title="Exclusive Deals"
            icon={<Tag className="h-4 w-4 text-blue-500" />}
          >
            {/* Access to affiliate deals */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300">
                Access to affiliate deals
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center items-center text-xs text-primary-foreground/80 px-1 py-3 text-center">
                Public deals
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 text-xs text-center px-1 py-3">
                Public deals
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 text-xs text-center px-1 py-3">
                Private + High-Paying Deals
              </div>
            </div>
          </FeatureSection>

          {/* Affitor Hunt Section */}
          <FeatureSection
            title="Affitor Hunt"
            icon={<Search className="h-4 w-4 text-blue-500" />}
          >
            {/* Traffic share */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300">
                Traffic share
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center text-xs text-primary-foreground/80 px-1 py-3">
                {basicTrafficRatePercent}%
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 text-xs px-1 py-3">
                {proTrafficRatePercent}%
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 text-xs px-1 py-3">
                {premiumTrafficRatePercent}%
              </div>
            </div>
          </FeatureSection>

          {/* Program Submission Section */}
          <FeatureSection
            title="Program Submission"
            icon={<Upload className="h-4 w-4 text-blue-500" />}
          >
            {/* Suggest new tools */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300">
                Suggest new tools
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center text-xs text-primary-foreground/80 px-1 py-3">
                Waitlist
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 text-xs px-1 py-3">
                Priority
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 text-xs px-1 py-3">
                Priority
              </div>
            </div>
          </FeatureSection>

          {/* Feature Access Section */}
          <FeatureSection
            title="Feature Access"
            icon={<Key className="h-4 w-4 text-blue-500" />}
          >
            {/* Early access to new tools */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300">
                Early access to new tools
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center px-1 py-3">
                <XCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 px-1 py-3">
                <CheckCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 px-1 py-3">
                <CheckCircleIcon />
              </div>
            </div>
          </FeatureSection>

          {/* Affitor AI Section */}
          <FeatureSection
            title="Affitor AI"
            icon={<Bot className="h-4 w-4 text-blue-500" />}
          >
            {/* Usage & features */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300">
                Usage & features
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center text-xs text-primary-foreground/80 px-1 py-3">
                Limited
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 text-xs px-1 py-3">
                Limited
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 text-xs px-1 py-3">
                Unlimited
              </div>
            </div>

            {/* AI tools */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300 flex items-center group relative">
                AI tools
                <div className="relative inline-block ml-1">
                  {/* Modified info icon with better mobile support */}
                  <span className="inline-flex items-center justify-center w-5 h-5 cursor-help">
                    <Info className="h-3.5 w-3.5 text-gray-400" />
                  </span>
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 px-2 py-1 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity duration-200 z-10 whitespace-normal text-center before:content-[''] before:absolute before:top-full before:left-1/2 before:-translate-x-1/2 before:border-4 before:border-transparent before:border-t-gray-800">
                    script generation, product summaries, analysis
                  </div>
                </div>
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center text-xs text-primary-foreground/80 px-1 py-3">
                Basic
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 text-xs px-1 py-3">
                Advanced AI model
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 text-xs px-1 py-3">
                Advanced AI model
              </div>
            </div>
          </FeatureSection>

          {/* Trust & Support Section */}
          <FeatureSection
            title="Trust & Support"
            icon={<ShieldCheck className="h-4 w-4 text-blue-500" />}
          >
            {/* Blue check badge */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300 flex flex-wrap items-center">
                Blue check badge
                <span className="ml-1 mt-1 sm:mt-0 sm:ml-2 text-xs bg-purple-100 text-purple-800 px-1 rounded">
                  Soon
                </span>
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center px-1 py-3">
                <XCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 px-1 py-3">
                <CheckCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 px-1 py-3">
                <CheckCircleIcon />
              </div>
            </div>

            {/* Higher trust & visibility */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300">
                Higher trust & visibility
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center px-1 py-3">
                <XCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 px-1 py-3">
                <CheckCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 px-1 py-3">
                <CheckCircleIcon />
              </div>
            </div>

            {/* VIP Support */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300">
                VIP Support (24h response)
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center px-1 py-3">
                <XCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 px-1 py-3">
                <XCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 px-1 py-3">
                <CheckCircleIcon />
              </div>
            </div>

            {/* Video training */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300 flex items-center">
                Video training
                <Video className="ml-2 h-3 w-3 text-gray-400" />
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center px-1 py-3">
                <XCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 px-1 py-3">
                <CheckCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 px-1 py-3">
                <CheckCircleIcon />
              </div>
            </div>

            {/* Private Group */}
            <div className="grid grid-cols-12 gap-0 items-center border-b">
              <div className="col-span-4 sm:col-span-4 pl-4 sm:pl-8 p-3 text-xs sm:text-sm text-gray-600 dark:text-gray-300 flex items-center">
                Private Group
                <Users className="ml-2 h-3 w-3 text-gray-400" />
              </div>
              <div className="col-span-2 sm:col-span-2 flex justify-center px-1 py-3">
                <XCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 px-1 py-3">
                <XCircleIcon />
              </div>
              <div className="col-span-3 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 px-1 py-3">
                <CheckCircleIcon />
              </div>
            </div>
          </FeatureSection>

          {/* Pricing Section with correct prices directly from tier data */}
          <div className="col-span-12 grid grid-cols-12 gap-0 items-center border-t mt-4 sticky bottom-0 bg-background/95 backdrop-blur-sm shadow-sm -mb-2">
            <div className="col-span-4 sm:col-span-4 pl-4 p-3 text-sm font-medium">
              Pricing
            </div>
            <div className="col-span-2 sm:col-span-2 flex flex-col justify-center text-xs text-primary-foreground/80 px-1 py-3 text-center">
              <div className="text-sm font-bold">$0/{getPeriodLabel}</div>
              {/* <div className="text-xs text-gray-600 dark:text-gray-400"> */}
              {/* ($0/month) */}
              {/* </div> */}
              <div className="mt-1">
                {billingPeriod !== "monthly" ? "Free" : ""}
              </div>
            </div>
            <div className="col-span-3 sm:col-span-3 flex flex-col justify-center bg-green-50 dark:bg-green-900/30 text-xs px-1 py-3 text-center relative">
              <div className="text-sm font-bold">
                {proCurrentPeriodPrice === 0 ? (
                  "Contact Sales"
                ) : (
                  <>
                    $
                    {formatCurrency(
                      billingPeriod === "yearly"
                        ? proCurrentPeriodPrice * 12
                        : billingPeriod === "quarterly"
                        ? proCurrentPeriodPrice * 3
                        : proCurrentPeriodPrice
                    )}
                    /{getPeriodLabel}
                  </>
                )}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                {billingPeriod !== "monthly" && proTier?.price
                  ? `$${proTier.price}/month billed ${
                      billingPeriod === "yearly" ? "annually" : "quarterly"
                    }`
                  : ""}
                {proSavingsPercent > 0 && ` - ${proSavingsPercent}% Off`}
              </div>
              {billingPeriod !== "monthly" && proMonthlyPrice > 0 && (
                <div className="mt-1">
                  or ${formatCurrency(proMonthlyPrice)}/month
                </div>
              )}
            </div>
            <div className="col-span-3 sm:col-span-3 flex flex-col justify-center bg-blue-50 dark:bg-blue-900/30 text-xs px-1 py-3 text-center relative">
              <div className="text-sm font-bold">
                {premiumCurrentPeriodPrice === 0 ? (
                  "Contact Sales"
                ) : (
                  <>
                    $
                    {formatCurrency(
                      billingPeriod === "yearly"
                        ? premiumCurrentPeriodPrice * 12
                        : billingPeriod === "quarterly"
                        ? premiumCurrentPeriodPrice * 3
                        : premiumCurrentPeriodPrice
                    )}
                    /{getPeriodLabel}
                  </>
                )}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                {billingPeriod !== "monthly" && premiumTier?.price
                  ? `$${premiumTier.price}/month billed ${
                      billingPeriod === "yearly" ? "annually" : "quarterly"
                    }`
                  : ""}
                {premiumSavingsPercent > 0 &&
                  ` - ${premiumSavingsPercent}% Off`}
              </div>
              {billingPeriod !== "monthly" && premiumMonthlyPrice > 0 && (
                <div className="mt-1">
                  or ${formatCurrency(premiumMonthlyPrice)}/month
                </div>
              )}
            </div>
          </div>

          {/* Call to action buttons - Match column spans */}
          <div className="col-span-12 grid grid-cols-12 gap-0 items-center mt-2">
            <div className="hidden sm:block sm:col-span-4 min-h-[52px]"></div>
            <div className="col-span-2 sm:col-span-2 flex justify-center px-1 py-2 sm:py-4 min-h-[50px] sm:min-h-[68px]">
              <Button
                variant="default"
                className="w-full text-xs sm:text-sm h-12 sm:h-10 font-medium bg-primary text-primary-foreground hover:bg-primary/90 dark:hover:bg-primary/80 border border-primary-foreground/20 transition-all duration-200 hover:shadow-md hover:-translate-y-0.5"
                onClick={handleStartFree}
                disabled={isLoading || isOnBasicPlan}
              >
                {isLoading ? (
                  <span className="flex items-center justify-center">
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Processing...
                  </span>
                ) : isOnBasicPlan ? (
                  "Current Plan"
                ) : (
                  "Start Free"
                )}
              </Button>
            </div>
            <div className="col-span-5 sm:col-span-3 flex justify-center bg-green-50 dark:bg-green-900/30 px-1 py-2 sm:py-4 min-h-[50px] sm:min-h-[68px]">
              <Button
                variant="outline"
                className="w-full mx-0 bg-primary-foreground text-primary hover:bg-muted hover:shadow-md hover:-translate-y-0.5 border border-primary dark:bg-blue-950 dark:text-blue-300 dark:border-blue-700 dark:hover:bg-blue-800 dark:hover:border-blue-500 font-medium text-xs sm:text-sm h-12 sm:h-10 transition-all duration-200"
                onClick={handleGetPro}
                disabled={isLoading || isOnProPlan}
              >
                {isLoading ? (
                  <span className="flex items-center justify-center">
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-primary dark:text-slate-200"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Processing...
                  </span>
                ) : isOnProPlan ? (
                  "Current Plan"
                ) : (
                  getProButtonText
                )}
              </Button>
            </div>
            <div className="col-span-5 sm:col-span-3 flex justify-center bg-blue-50 dark:bg-blue-900/30 px-1 py-2 sm:py-4 rounded-b-lg min-h-[50px] sm:min-h-[68px]">
              <Button
                variant="outline"
                className="w-full mx-0 bg-primary-foreground text-primary hover:bg-muted hover:shadow-md hover:-translate-y-0.5 border border-primary dark:bg-blue-950 dark:text-blue-300 dark:border-blue-700 dark:hover:bg-blue-800 dark:hover:border-blue-500 font-medium text-xs sm:text-sm h-12 sm:h-10 transition-all duration-200"
                onClick={handleGetPremium}
                disabled={isLoading || isOnPremiumPlan}
              >
                {isLoading ? (
                  <span className="flex items-center justify-center">
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-primary dark:text-blue-300"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Processing...
                  </span>
                ) : isOnPremiumPlan ? (
                  "Current Plan"
                ) : (
                  getPremiumButtonText
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile-optimized tooltips - add at the end of component */}
      <style jsx global>{`
        @media (max-width: 768px) {
          /* Improve tooltip behavior on mobile */
          .relative.inline-block:active .invisible {
            visibility: visible;
            opacity: 1;
            transition-delay: 0s;
          }

          /* Bigger touch targets for mobile */
          .cursor-help {
            min-height: 24px;
            min-width: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
          }

          /* Keep the actual icon small */
          .cursor-help svg {
            height: 10px;
            width: 10px;
          }

          /* Prevent tooltips from being cut off */
          .absolute.bottom-full {
            max-width: 85vw;
            left: 0;
            transform: translateX(0);
          }

          /* Improve arrow positioning */
          .before\\:absolute:before {
            left: 10px;
          }

          /* Make rows more distinguishable */
          .border-b {
            border-width: 2px;
            border-color: rgba(229, 231, 235, 0.3);
          }

          /* Add more space between rows */
          .grid.grid-cols-12.gap-0.items-center.border-b {
            padding-top: 4px;
            padding-bottom: 4px;
          }

          /* Better badge visibility on mobile */
          .rounded-full {
            white-space: nowrap;
            font-size: 0.65rem;
            overflow: visible;
          }

          /* Improve table layout on small screens */
          .min-w-\\[600px\\] {
            min-width: 600px;
          }

          /* Better touch targets on mobile */
          .col-span-2,
          .col-span-3 {
            min-width: 70px;
          }
        }
      `}</style>
    </div>
  );
};

export default CompareTier;
