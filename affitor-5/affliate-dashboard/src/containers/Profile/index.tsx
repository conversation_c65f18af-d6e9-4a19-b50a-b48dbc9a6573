import Link from "next/link";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import { useToast } from "@/context/ToastContext";
import { actions as authActions } from "@/features/auth/auth.slice";
import { actions as userActions } from "@/features/user/user.slice";
import { actions as subscriptionActions } from "@/features/subscription-tier/subscription-tier.slice";
import { actions as pageActions } from "@/features/page/page.slice";
import {
  selectUserData,
  selectUserLoading,
  selectUserError,
} from "@/features/selectors";
import {
  selectCreatePageLoading,
  selectPageData,
  selectPageLoading,
  selectPageError,
  selectDeletePageLoading,
  selectDeletePageError,
  selectPageSuccessMessage,
} from "@/features/page/page.slice";
import {
  LogOut,
  Mail,
  Calendar,
  PieChart,
  Tag,
  Clock,
  History,
  User,
  Loader,
  AlertCircle,
  Plus,
  FileText,
  Edit,
  Trash2,
  Co<PERSON>,
  Check,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

// Delete Confirmation Dialog Component
interface DeleteConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  itemName: string;
  itemType: string;
  isLoading?: boolean;
  itemStatus?: string;
  itemCreatedAt?: string;
}

const DeleteConfirmationDialog: React.FC<DeleteConfirmationDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  itemName,
  itemType,
  isLoading = false,
  itemStatus,
  itemCreatedAt,
}) => {
  const isPublished = itemStatus === 'published';
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="sm:max-w-[425px]">
        <AlertDialogHeader>
          <div className="flex items-center mb-2">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
              <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
            </div>
            <div className="ml-3">
              <AlertDialogTitle className="text-lg font-semibold text-gray-900 dark:text-white">
                Delete {itemType}
              </AlertDialogTitle>
            </div>
          </div>
          <AlertDialogDescription className="text-sm text-gray-500 dark:text-gray-400 mt-2">
            Are you sure you want to delete <span className="font-medium text-gray-900 dark:text-white">"{itemName}"</span>?
            This action cannot be undone and will permanently remove the page and all its content.
            {isPublished && (
              <div className="mt-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded text-yellow-800 dark:text-yellow-200 text-xs">
                ⚠️ <strong>Warning:</strong> This is a published page that may be publicly accessible. Deleting it will break any existing links.
              </div>
            )}
            {itemType === "Page" && (
              <div className="mt-2 text-xs text-gray-400">
                This will also remove any associated analytics data and referrer links.
                {itemCreatedAt && (
                  <div className="mt-1">
                    Created: {new Date(itemCreatedAt).toLocaleDateString()}
                  </div>
                )}
              </div>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex gap-2 sm:gap-0">
          <AlertDialogCancel
            disabled={isLoading}
            className="mt-3 sm:mt-0"
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-500 dark:bg-red-600 dark:hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <>
                <Loader className="w-4 h-4 mr-2 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="w-4 h-4 mr-2" />
                Delete {itemType}
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export const Profile = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { showToast } = useToast();
  const [redirecting, setRedirecting] = useState(false);
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [pageToDelete, setPageToDelete] = useState<any>(null);
  const [copiedPageId, setCopiedPageId] = useState<string | null>(null);

  const userData = useSelector(selectUserData);
  const loading = useSelector(selectUserLoading);
  const error = useSelector(selectUserError);
  const createPageLoading = useSelector(selectCreatePageLoading);
  const userPages = useSelector(selectPageData);
  const pagesLoading = useSelector(selectPageLoading);
  const pagesError = useSelector(selectPageError);
  const deletePageLoading = useSelector(selectDeletePageLoading);
  const deletePageError = useSelector(selectDeletePageError);
  const successMessage = useSelector(selectPageSuccessMessage);

  useEffect(() => {
    console.log('👤 [Profile] Component mounted - fetching user data and pages');
    dispatch(userActions.fetchUserMe());
    // Fetch user's pages
    dispatch(pageActions.fetchPagesRequest({ page: 1, pageSize: 5 }));
  }, [dispatch]);

  // Track Redux state changes for debugging
  useEffect(() => {
    console.log('👤 [Profile] Redux state changed:', {
      userPagesCount: userPages?.length || 0,
      pagesLoading,
      pagesError,
      createPageLoading,
      userLoading: loading,
      userError: error,
    });

    if (userPages && userPages.length > 0) {
      console.log('👤 [Profile] User pages data:', userPages.map(page => ({
        id: page.id,
        documentId: page.documentId,
        title: page.title,
        status: page.status,
      })));
    }
  }, [userPages, pagesLoading, pagesError, createPageLoading, loading, error]);

  // Refresh pages list when page creation is no longer loading (success or failure)
  useEffect(() => {
    if (!createPageLoading) {
      console.log('👤 [Profile] Page creation finished - refreshing pages list');
      // Small delay to ensure the backend has processed the creation
      const timer = setTimeout(() => {
        dispatch(pageActions.fetchPagesRequest({ page: 1, pageSize: 5 }));
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [createPageLoading, dispatch]);

  // Refresh pages list when page deletion is no longer loading (success or failure)
  useEffect(() => {
    if (!deletePageLoading) {
      console.log('👤 [Profile] Page deletion finished - refreshing pages list');
      // Small delay to ensure the backend has processed the deletion
      const timer = setTimeout(() => {
        dispatch(pageActions.fetchPagesRequest({ page: 1, pageSize: 5 }));
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [deletePageLoading, dispatch]);

  // Handle delete success message
  useEffect(() => {
    if (successMessage) {
      showToast('Success', successMessage, 'success');
      // Clear the message after showing
      dispatch(pageActions.clearSuccessMessage());
    }
  }, [successMessage, showToast, dispatch]);

  // Handle delete error message
  useEffect(() => {
    if (deletePageError) {
      showToast('Error', deletePageError, 'destructive');
      // Clear the error after showing
      dispatch(pageActions.clearErrors());
    }
  }, [deletePageError, showToast, dispatch]);

  const handleSignOut = () => {
    dispatch(authActions.logout());
    dispatch(userActions.clearUserData());
    // Use router.push instead of router.replace to avoid conflicts
    // Also, let the auth saga handle the redirect by removing this line
    // router.replace("/authentication");
  };

  // Handle page creation
  const handleCreatePage = () => {
    console.log('👤 [Profile] Creating new page');
    const newPageData = {
      title: 'Untitled Page',
      content: {
        'block-1': {
          id: 'block-1',
          type: 'paragraph' as const,
          value: [{ text: 'Start writing your content...' }],
          meta: { order: 0, depth: 0 }
        }
      },
      excerpt: '',
      status: 'draft' as const,
    };

    console.log('👤 [Profile] Dispatching createPageRequest with data:', newPageData);
    dispatch(pageActions.createPageRequest(newPageData));
  };

  // Navigate to page editor when page is created
  useEffect(() => {
    // This would be triggered by a success action in a real implementation
    // For now, we'll handle navigation in the saga or component
  }, []);

  // Handle page edit
  const handleEditPage = (pageId: string) => {
    router.push(`/editor/${pageId}`);
  };

  const handleCopyLink = async (pageId: string) => {
    try {
      const url = `${window.location.origin}/editor/${pageId}`;
      await navigator.clipboard.writeText(url);
      setCopiedPageId(pageId);
      // showToast('Link copied to clipboard!', 'success');

      // Reset the copied state after 2 seconds
      setTimeout(() => {
        setCopiedPageId(null);
      }, 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
      showToast('Failed to copy link', 'error');
    }
  };



  // Handle page delete
  const handleDeletePage = (page: any) => {
    setPageToDelete(page);
    setIsDeleteDialogOpen(true);
  };

  // Confirm page deletion
  const confirmDeletePage = () => {
    if (pageToDelete) {
      dispatch(pageActions.deletePageRequest(pageToDelete.documentId));
      setIsDeleteDialogOpen(false);
      setPageToDelete(null);
    }
  };

  // Cancel page deletion
  const cancelDeletePage = () => {
    setIsDeleteDialogOpen(false);
    setPageToDelete(null);
  };

  // Handle 401 error and redirect
  useEffect(() => {
    if (error && error.includes("401")) {
      setRedirecting(true);
      const timer = setTimeout(() => {
        // Use router.push instead of router.replace for better navigation
        router.push("/authentication?redirect=/profile");
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [error, router]);

  // Calculate usage data from the API response
  const subscription_tier = userData?.user_tracking_request?.subscription_tier;
  const transaction = userData?.user_tracking_request?.transaction;
  const request_limit = userData?.user_tracking_request?.request_limit || 0;

  // Check if subscription is cancelled but still active (grace period)
  const isCancelled =
    transaction?.cancellation_date !== null &&
    transaction?.cancellation_date !== undefined;
  const willAutoRenew = transaction?.auto_renew === true;

  // Check if user has unlimited requests (request_limit > 10000)
  const hasUnlimitedRequests = request_limit > 10000;

  const usageData = userData?.user_tracking_request
    ? {
        usageCount: userData.user_tracking_request.request_count,
        maxRequests: userData.user_tracking_request.request_limit,
        last_request_date: new Date(
          userData.user_tracking_request.last_request_date
        ),
        // Calculate days left using current_period_end
        daysLeft: userData.user_tracking_request.current_period_end
          ? Math.max(
              0,
              Math.floor(
                (new Date(
                  userData.user_tracking_request.current_period_end
                ).getTime() -
                  new Date().getTime()) /
                  (1000 * 60 * 60 * 24)
              )
            )
          : 30,
        tier: subscription_tier,
        isCancelled: isCancelled,
        willAutoRenew: willAutoRenew,
        cancellationDate: transaction?.cancellation_date
          ? new Date(transaction.cancellation_date)
          : null,
        cancellationReason: transaction?.cancellation_reason,
        currentPeriodEnd: transaction?.current_period_end
          ? new Date(transaction.current_period_end)
          : null,
        hasUnlimitedRequests: hasUnlimitedRequests,
      }
    : {
        usageCount: 0,
        maxRequests: 0,
        daysLeft: 30,
        tier: subscription_tier,
        isCancelled: false,
        willAutoRenew: false,
        cancellationDate: null,
        cancellationReason: null,
        currentPeriodEnd: null,
        hasUnlimitedRequests: false,
      };

  const usagePercentage =
    usageData.maxRequests > 0
      ? (usageData.usageCount / usageData.maxRequests) * 100
      : 0;

  // Check if user is on a basic tier (for daily request tracking)
  const isBasicTier = () => {
    if (!subscription_tier?.name) return false;
    const tierName = subscription_tier.name.toLowerCase();
    return tierName.includes('basic');
  };

  // Get daily request data for basic tier users
  const dailyRequestData = isBasicTier() && userData?.user_tracking_request ? {
    dailyUsed: userData.user_tracking_request.daily_request_count || 0,
    dailyLimit: userData.user_tracking_request.daily_request_limit || 0,
    dailyRemaining: Math.max(0, (userData.user_tracking_request.daily_request_limit || 0) - (userData.user_tracking_request.daily_request_count || 0)),
    lastDailyReset: userData.user_tracking_request.last_daily_reset_date,
  } : null;

  const dailyUsagePercentage = dailyRequestData && dailyRequestData.dailyLimit > 0
    ? (dailyRequestData.dailyUsed / dailyRequestData.dailyLimit) * 100
    : 0;

  const formatDate = (date?: string): string => {
    return date
      ? new Date(date).toLocaleDateString("en-US", {
          year: "numeric",
          month: "short",
          day: "numeric",
        })
      : "N/A";
  };

  const handleCancelSubscription = () => {
    dispatch(subscriptionActions.cancelSubscription());
    setIsCancelDialogOpen(false);
  };

  // Determine if user has a paid subscription
  const hasPaidSubscription = !!subscription_tier;

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto p-6 flex flex-col items-center justify-center min-h-[60vh]">
        <Loader className="w-10 h-10 text-blue-500 animate-spin mb-4" />
        <p className="text-gray-600 dark:text-gray-400">
          Loading your profile...
        </p>
      </div>
    );
  }

  if (error) {
    const is401Error = error.includes("401");

    return (
      <div className="max-w-6xl mx-auto p-6 flex flex-col items-center justify-center min-h-[60vh]">
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6 max-w-md w-full text-center">
          <div className="flex justify-center mb-4">
            <AlertCircle className="h-12 w-12 text-red-500" />
          </div>
          <h2 className="text-xl font-semibold mb-2 dark:text-white">
            {is401Error ? "Authentication Error" : "Something went wrong"}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {is401Error
              ? "Your session has expired or you are not authenticated."
              : ``}
          </p>
          {is401Error && (
            <>
              {redirecting ? (
                <p className="text-sm text-blue-600 dark:text-blue-400">
                  Redirecting to sign in...
                </p>
              ) : (
                <Button
                  onClick={() =>
                    router.push("/authentication?redirect=/profile")
                  }
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Sign In
                </Button>
              )}
            </>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-2 dark:text-white">User Settings</h1>
      <p className="text-gray-600 dark:text-gray-400 mb-6">
        You can manage your profile and account settings here.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
        {/* Left column - 30% */}
        <div className="md:col-span-4">
          {/* Basic Info Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold dark:text-white">
                Basic Information
              </h2>
              <button
                onClick={handleSignOut}
                className="text-gray-600 hover:text-red-600 dark:text-gray-300 dark:hover:text-red-400 transition-colors"
                title="Sign Out"
                aria-label="Sign Out"
              >
                <LogOut className="w-5 h-5" />
              </button>
            </div>

            {/* User avatar and details */}
            <div className="flex flex-col items-center mb-4">
              <div className="w-20 h-20 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-3">
                <User className="w-10 h-10 text-blue-600 dark:text-blue-300" />
              </div>
              <h3 className="text-lg font-medium dark:text-white">
                {userData?.username || "Anonymous User"}
              </h3>
            </div>

            {/* User information with icons */}
            <div className="space-y-3 mt-2">
              <div className="flex items-center text-gray-700 dark:text-gray-300">
                <Mail className="w-5 h-5 text-blue-500 mr-2 flex-shrink-0" />
                <span className="font-medium mr-2">Email:</span>
                <span className="text-gray-600 dark:text-gray-400 truncate">
                  {userData?.email || "N/A"}
                </span>
              </div>

              <div className="flex items-center text-gray-700 dark:text-gray-300">
                <Calendar className="w-5 h-5 text-blue-500 mr-2 flex-shrink-0" />
                <span className="font-medium mr-2">Joined:</span>
                <span className="text-gray-600 dark:text-gray-400">
                  {formatDate(userData?.createdAt)}
                </span>
              </div>
            </div>
          </div>

          {/* Account Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-3 dark:text-white">
              Manage Subscription
            </h2>
            <div className="ml-4">
              <p className="mb-2 dark:text-gray-200">
                Current plan: {subscription_tier?.display_name || "Free"}
                {usageData.isCancelled && (
                  <span className="ml-1 text-red-500 text-sm">(Cancelled)</span>
                )}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                {hasPaidSubscription
                  ? usageData.isCancelled
                    ? `Access until ${formatDate(
                        usageData.currentPeriodEnd?.toISOString()
                      )}`
                    : "Manage your subscription below"
                  : "Upgrade to unlock more usage"}
              </p>

              {/* Updated subscription management buttons */}
              <div className="flex flex-col gap-3">
                <Link
                  href={"/profile/upgrade"}
                  className={
                    "bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition-colors w-full text-center"
                  }
                >
                  {usageData.isCancelled
                    ? "Select Another Plan"
                    : hasPaidSubscription
                    ? "Change Plan"
                    : "Upgrade"}
                </Link>

                {hasPaidSubscription && !usageData.isCancelled && (
                  <Dialog
                    open={isCancelDialogOpen}
                    onOpenChange={setIsCancelDialogOpen}
                  >
                    <DialogTrigger asChild>
                      <Button
                        variant="ghost"
                        className="text-gray-500 hover:text-red-600 hover:bg-transparent dark:text-gray-400 dark:hover:text-red-400 transition-colors px-0 py-0 h-auto font-normal w-auto mx-auto"
                      >
                        Cancel my subscription
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[500px] w-[95vw]">
                      <DialogHeader>
                        <DialogTitle>Cancel Subscription</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to cancel your subscription?
                          You'll lose access to premium features at the end of
                          your current billing period.
                        </DialogDescription>
                      </DialogHeader>
                      <DialogFooter className="mt-4 flex-col space-y-2 sm:space-y-0 sm:space-x-2">
                        <Button
                          type="button"
                          variant="outline"
                          className="sm:flex-1 w-full"
                          onClick={() => setIsCancelDialogOpen(false)}
                        >
                          Keep Subscription
                        </Button>
                        <Button
                          type="button"
                          variant="destructive"
                          className="bg-red-600 hover:bg-red-700 text-white sm:flex-1 w-full"
                          onClick={handleCancelSubscription}
                        >
                          Cancel Subscription
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                )}
              </div>
            </div>
          </div>

          {/* Page Management Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-4">
              <h2 className="text-lg font-semibold dark:text-white flex items-center gap-2">
                <FileText className="w-5 h-5 text-blue-500 flex-shrink-0" />
                <span className="truncate">My Pages</span>
              </h2>
              <Button
                onClick={handleCreatePage}
                disabled={createPageLoading}
                className="group bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 whitespace-nowrap transition-all duration-200 ease-in-out hover:scale-105 active:scale-95 focus:ring-2 focus:ring-blue-500/20 focus:outline-none shadow-md hover:shadow-lg disabled:hover:scale-100 disabled:cursor-not-allowed"
                size="sm"
              >
                {createPageLoading ? (
                  <Loader className="w-4 h-4 animate-spin" />
                ) : (
                  <Plus className="w-4 h-4 transition-transform duration-200 group-hover:scale-110 group-hover:rotate-90" />
                )}
                <span className="hidden sm:inline">Generate Page</span>
                <span className="sm:hidden">New</span>
              </Button>
            </div>

            {/* Pages List */}
            <div className="space-y-3">
              {pagesLoading || deletePageLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader className="w-6 h-6 animate-spin text-blue-500" />
                  <span className="ml-2 text-gray-600 dark:text-gray-400">
                    {deletePageLoading ? "Deleting page..." : "Loading your pages..."}
                  </span>
                </div>
              ) : pagesError ? (
                <div className="text-center py-8">
                  <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
                  <p className="text-red-600 dark:text-red-400 mb-4">
                    Failed to load your pages: {pagesError}
                  </p>
                  <Button
                    onClick={() => dispatch(pageActions.fetchPagesRequest({ page: 1, pageSize: 5 }))}
                    variant="outline"
                    size="sm"
                  >
                    Try Again
                  </Button>
                </div>
              ) : userPages.length > 0 ? (
                userPages.slice(0, 3).map((page) => (
                  <div
                    key={page.documentId}
                    className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg gap-3"
                  >
                    <div className="flex-1 min-w-0">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <h3
                            className="font-medium text-gray-900 dark:text-white truncate cursor-default max-w-full sm:max-w-[180px] md:max-w-[220px] lg:max-w-[280px]"
                            title={page.title}
                          >
                            {page.title}
                          </h3>
                        </TooltipTrigger>
                        <TooltipContent
                          side="top"
                          className="max-w-xs bg-primary text-primary-foreground border border-border shadow-lg"
                          sideOffset={5}
                        >
                          <p className="break-words leading-relaxed">{page.title}</p>
                        </TooltipContent>
                      </Tooltip>
                      <div className="flex items-center gap-2 mt-1 flex-wrap">
                        <span className={`px-2 py-1 text-xs rounded-full whitespace-nowrap ${
                          page.status === 'published'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        }`}>
                          {page.status}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap">
                          {new Date(page.updatedAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-1 sm:gap-2 flex-shrink-0">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopyLink(page.documentId)}
                            className="group relative p-2 text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 ease-in-out hover:scale-105 active:scale-95 focus:ring-2 focus:ring-blue-500/20 focus:outline-none rounded-md"
                          >
                            {copiedPageId === page.documentId ? (
                              <Check className="w-4 h-4 text-green-600 transition-transform duration-200 group-hover:scale-110" />
                            ) : (
                              <Copy className="w-4 h-4 transition-transform duration-200 group-hover:scale-110" />
                            )}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent side="top">
                          <p>{copiedPageId === page.documentId ? 'Copied!' : 'Copy Link'}</p>
                        </TooltipContent>
                      </Tooltip>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditPage(page.documentId)}
                            className="group relative p-2 text-gray-600 hover:text-emerald-600 dark:text-gray-400 dark:hover:text-emerald-400 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 transition-all duration-200 ease-in-out hover:scale-105 active:scale-95 focus:ring-2 focus:ring-emerald-500/20 focus:outline-none rounded-md"
                          >
                            <Edit className="w-4 h-4 transition-transform duration-200 group-hover:scale-110 group-hover:rotate-3" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent side="top">
                          <p>Edit Page</p>
                        </TooltipContent>
                      </Tooltip>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeletePage(page)}
                            disabled={deletePageLoading}
                            className="group relative p-2 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200 ease-in-out hover:scale-105 active:scale-95 focus:ring-2 focus:ring-red-500/20 focus:outline-none rounded-md disabled:opacity-50 disabled:hover:scale-100 disabled:cursor-not-allowed"
                          >
                            {deletePageLoading && pageToDelete?.documentId === page.documentId ? (
                              <Loader className="w-4 h-4 animate-spin" />
                            ) : (
                              <Trash2 className="w-4 h-4 transition-transform duration-200 group-hover:scale-110 group-hover:rotate-3" />
                            )}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent side="top">
                          <p>Delete Page</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400 mb-4">
                    You haven't created any pages yet.
                  </p>
                  <Button
                    onClick={handleCreatePage}
                    disabled={createPageLoading}
                    className="group bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 transition-all duration-200 ease-in-out hover:scale-105 active:scale-95 focus:ring-2 focus:ring-blue-500/20 focus:outline-none shadow-md hover:shadow-lg disabled:hover:scale-100 disabled:cursor-not-allowed"
                  >
                    {createPageLoading ? (
                      <Loader className="w-4 h-4 animate-spin" />
                    ) : (
                      <Plus className="w-4 h-4 transition-transform duration-200 group-hover:scale-110 group-hover:rotate-90" />
                    )}
                    Create Your First Page
                  </Button>
                </div>
              )}

              {userPages.length > 3 && (
                <div className="text-center pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push('/my-pages')}
                    className="group text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300 hover:bg-blue-50 dark:border-blue-800 dark:hover:border-blue-700 dark:hover:bg-blue-900/20 transition-all duration-200 ease-in-out hover:scale-105 active:scale-95 focus:ring-2 focus:ring-blue-500/20 focus:outline-none"
                  >
                    <span className="transition-transform duration-200 group-hover:translate-x-1">
                      View All Pages ({userPages.length})
                    </span>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right column - 70% */}
        <div className="md:col-span-8">
          {/* Usage Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 h-full">
            <div className="flex items-center mb-5">
              <PieChart className="w-6 h-6 text-blue-500 mr-3" />
              <h2 className="text-lg font-semibold dark:text-white">
                Usage Statistics
              </h2>
            </div>

            {/* Monthly Usage Progress */}
            <div className="mb-6 w-full">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Monthly Free Actions
                </span>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {usageData.hasUnlimitedRequests ? (
                    "Unlimited"
                  ) : (
                    <>
                      {usageData.usageCount}/{usageData.maxRequests} used -{" "}
                      {usageData.maxRequests - usageData.usageCount} left this
                      month
                    </>
                  )}
                </span>
              </div>
              <Tooltip>
                <TooltipTrigger className="w-full">
                  <Progress
                    className="h-3 rounded-full w-full bg-gray-200"
                    value={
                      usageData.hasUnlimitedRequests ? 100 : usagePercentage
                    }
                    color={
                      usageData.hasUnlimitedRequests
                        ? "green"
                        : usagePercentage > 75
                        ? "red"
                        : usagePercentage > 50
                        ? "yellow"
                        : "green"
                    }
                  />
                </TooltipTrigger>
                <TooltipContent className="bg-primary text-primary-foreground border border-border shadow-lg max-w-sm">
                  <p className="break-words leading-relaxed">
                    {usageData.hasUnlimitedRequests
                      ? "You have unlimited usage with your premium subscription."
                      : "Each time you use features like Social Listen, AI Script, or Affitor Chat, 1 Action is used."}
                  </p>
                </TooltipContent>
              </Tooltip>
              <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                {usageData.hasUnlimitedRequests ? (
                  <span>Unlimited usage available</span>
                ) : (
                  <>
                    <span>{usagePercentage.toFixed(1)}% used</span>
                    <span>{(100 - usagePercentage).toFixed(1)}% remaining</span>
                  </>
                )}
              </div>
            </div>

            {/* Daily Usage Progress for Basic Tier Users */}
            {dailyRequestData && dailyRequestData.dailyLimit > 0 && (
              <div className="mb-6 w-full border-t border-gray-200 dark:border-gray-700 pt-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Daily Actions (Basic Plan)
                  </span>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {dailyRequestData.dailyUsed}/{dailyRequestData.dailyLimit} used -{" "}
                    {dailyRequestData.dailyRemaining} left today
                  </span>
                </div>
                <Tooltip>
                  <TooltipTrigger className="w-full">
                    <Progress
                      className="h-3 rounded-full w-full bg-gray-200"
                      value={dailyUsagePercentage}
                      color={
                        dailyUsagePercentage > 75
                          ? "red"
                          : dailyUsagePercentage > 50
                          ? "yellow"
                          : "green"
                      }
                    />
                  </TooltipTrigger>
                  <TooltipContent className="bg-primary text-primary-foreground border border-border shadow-lg max-w-sm">
                    <p className="break-words leading-relaxed">
                      Basic plan users have a daily limit that resets at 00:00 UTC each day.
                    </p>
                  </TooltipContent>
                </Tooltip>
                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                  <span>{dailyUsagePercentage.toFixed(1)}% used today</span>
                  <span>Resets at 00:00 UTC</span>
                </div>
              </div>
            )}

            {/* Usage Details Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              {/* Subscription Card - Only shown if user has subscription */}
              {subscription_tier && (
                <div className="bg-secondary/50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <Tag className="w-5 h-5 text-blue-500 mr-2" />
                    <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-200">
                      Subscription Type
                    </h3>
                  </div>
                  <p className="text-lg font-bold text-gray-800 dark:text-white mb-1">
                    {subscription_tier?.display_name || "Free"}
                    {usageData.isCancelled && (
                      <span className="ml-2 text-sm font-normal text-red-500">
                        (Cancelled)
                      </span>
                    )}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {usageData.isCancelled
                      ? "Will not renew after current period"
                      : "Renews automatically at the end of billing period"}
                  </p>
                </div>
              )}

              {/* Time Remaining or Refresh Card - Shown for all users */}
              <div
                className={`bg-secondary/50 dark:bg-gray-700 rounded-lg p-4 ${
                  !subscription_tier ? "md:col-span-2" : ""
                }`}
              >
                <div className="flex items-center mb-2">
                  <Clock className="w-5 h-5 text-blue-500 mr-2" />
                  <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-200">
                    {subscription_tier ? "Billing Cycle" : "Request Quota"}
                  </h3>
                </div>
                {subscription_tier ? (
                  <>
                    <p className="text-lg font-bold text-gray-800 dark:text-white mb-1">
                      {usageData.daysLeft} days remaining
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {usageData.isCancelled
                        ? `Access ends on ${formatDate(
                            usageData.currentPeriodEnd?.toISOString()
                          )}`
                        : `Quota resets on ${formatDate(
                            new Date(
                              Date.now() +
                                usageData.daysLeft * 24 * 60 * 60 * 1000
                            ).toISOString()
                          )}`}
                    </p>
                  </>
                ) : (
                  <>
                    <p className="text-lg font-bold text-gray-800 dark:text-white mb-1">
                      Free Plan
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Requests will refresh on{" "}
                      {formatDate(getFirstDayOfNextMonth().toISOString())}
                    </p>
                  </>
                )}
              </div>

              {/* Cancellation info - Only shown if subscription is cancelled */}
              {usageData.isCancelled && (
                <div className="bg-red-50 dark:bg-red-900/30 rounded-lg p-4 md:col-span-2">
                  <div className="flex items-center">
                    <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
                    <div>
                      <h3 className="text-sm font-semibold text-red-700 dark:text-red-300">
                        Subscription Cancelled
                      </h3>
                      <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                        Your subscription was cancelled on{" "}
                        {formatDate(usageData.cancellationDate?.toISOString())}.
                        You will still have access to premium features until{" "}
                        {formatDate(usageData.currentPeriodEnd?.toISOString())}.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Usage History */}
            <div className="border-t dark:border-gray-700 pt-4">
              <div className="flex items-center mb-3">
                <History className="w-5 h-5 text-blue-500 mr-2" />
                <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-200">
                  Recent Activity
                </h3>
              </div>
              <div className="bg-secondary/50 dark:bg-gray-700 rounded-lg p-3 mb-2">
                <div className="flex justify-between items-center">
                  <div>
                    <span className="block text-sm font-medium text-gray-700 dark:text-white">
                      Last API Request
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {formatDate(
                        userData?.user_tracking_request?.last_request_date
                      )}
                    </span>
                  </div>
                  <span className="text-xs font-medium px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full">
                    Success
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={cancelDeletePage}
        onConfirm={confirmDeletePage}
        itemName={pageToDelete?.title || ""}
        itemType="Page"
        isLoading={deletePageLoading}
        itemStatus={pageToDelete?.status}
        itemCreatedAt={pageToDelete?.createdAt}
      />
    </div>
  );
};

const getFirstDayOfNextMonth = (): Date => {
  const today = new Date();
  // Create date for first day of next month
  return new Date(today.getFullYear(), today.getMonth() + 1, 1);
};

export default Profile;
