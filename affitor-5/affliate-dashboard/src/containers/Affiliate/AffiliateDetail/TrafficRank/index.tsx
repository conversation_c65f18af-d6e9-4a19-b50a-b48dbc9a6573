import { Loading } from "@/components";
import { selectTrafficWebList, selectErrorTrafficWeb } from "@/features/traffic-web/traffic-web.slice";
import { useSelector } from "react-redux";
// Option 1: Using Radix UI icons
import { GlobeIcon, FileIcon } from "@radix-ui/react-icons";
import { getCountryISOCode } from "@/utils/countries";

export default function TrafficRank() {
  const trafficWebs = useSelector(selectTrafficWebList);
  const error = useSelector(selectErrorTrafficWeb);

  // Helper function to format category string
  const formatCategory = (category: string): string => {
    return category
      .replace(/_/g, ' ')
      .replace(/\//g, ' > ');
  };

  return (
    <div className="p-4 bg-primary shadow rounded-lg h-full">
      <h2 className="text-lg md:text-xl font-bold mb-4 text-primary-foreground">Ranking</h2>

      {trafficWebs ? (
        <div>
          {trafficWebs.length > 0 ? (
            <div className="grid grid-cols-3 gap-2">
              {/* Global Rank Column */}
              <div className="text-center p-2 hover:bg-secondary rounded-md transition-colors mb-4 md:mb-0 md:border-b-0 md:border-r border-border pb-6 md:pb-4">
                <div className="text-[13px] whitespace-nowrap md:text-[16px] text-secondary-foreground font-semibold mb-2">
                  Global Rank
                </div>
                <div className="flex items-center justify-center text-[14px] md:text-xl font-bold">
                  <GlobeIcon className="h-5 w-5 mr-2 text-gray-600" />
                  {/* If using MUI: <PublicIcon className="h-5 w-5 mr-2 text-gray-600" /> */}
                  #{trafficWebs[0].global_rank?.toLocaleString() || "N/A"}
                </div>
                <div className="text-sm text-gray-600 mt-1">Worldwide</div>
              </div>

              {/* Country Rank Column */}
              <div className="text-center p-2 hover:bg-secondary rounded-md transition-colors mb-4 md:mb-0 md:border-b-0 md:border-r border-border pb-6 md:pb-4">
                <div className="text-[13px] whitespace-nowrap md:text-[16px] text-secondary-foreground font-semibold mb-2">
                  Country Rank
                </div>
                <div className="flex items-center justify-center text-[14px] md:text-xl font-bold">
                  {trafficWebs[0].country_rank?.country_code && (
                    <>
                      {(() => {
                        const isoCode =
                          trafficWebs[0].country_rank?.country_code;
                        return isoCode !== "xx" ? (
                          <img
                            src={`https://flagcdn.com/24x18/${isoCode.toLowerCase()}.png`}
                            alt={trafficWebs[0].country_rank?.country || ""}
                            className="h-5 w-5 mr-2"
                          />
                        ) : (
                          <div className="h-5 w-5 mr-2 bg-secondary rounded"></div>
                        );
                      })()}
                    </>
                  )}
                  #{trafficWebs[0].country_rank?.rank.toLocaleString() || "N/A"}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {trafficWebs[0].country_rank?.country_name || "Unknown"}
                </div>
              </div>

              {/* Category Rank Column */}
              <div className="text-center p-2 hover:bg-secondary rounded-md transition-colors">
                <div className="text-[13px] whitespace-nowrap md:text-[16px] text-secondary-foreground font-semibold mb-2">
                  Category Rank
                </div>
                <div className="flex items-center justify-center text-[14px] md:text-xl font-bold">
                  <FileIcon className="h-5 w-5 mr-2 text-gray-600" />
                  {/* If using MUI: <FolderIcon className="h-5 w-5 mr-2 text-gray-600" /> */}
                  #
                  {trafficWebs[0].category_rank?.rank.toLocaleString() || "N/A"}
                </div>
                {/* <div className="text-sm text-gray-600 mt-1">
                  {trafficWebs[0].category_rank?.category
                    ? formatCategory(trafficWebs[0].category_rank.category)
                    : "Unknown"}
                </div> */}
              </div>
            </div>
          ) : (
            <p className="text-xs md:text-sm">Ranking data is not available</p>
          )}
        </div>
      ) : !error ? (
        <Loading containerClassName="!h-[200px]" />
      ) : (
        <p className="text-xs md:text-sm text-red-400">
          Error loading ranking data
        </p>
      )}
    </div>
  );
}
