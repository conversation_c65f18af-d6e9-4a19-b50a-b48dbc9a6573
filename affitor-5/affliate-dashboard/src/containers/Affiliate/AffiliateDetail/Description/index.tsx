import { CheckIcon } from "../../../../components/Icons";
import { IAffiliate } from "@/interfaces";
import { BlocksRenderer } from '@strapi/blocks-react-renderer';
import { useState, useRef, useLayoutEffect } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";

export default function Detail({ program }: { program: IAffiliate }) {
    const [isExpanded, setIsExpanded] = useState(false);
    const [showButton, setShowButton] = useState(false);
    const contentRef = useRef<HTMLDivElement>(null);

    // Set a max height that's roughly equivalent to 40-50 lines of text.
    const MAX_HEIGHT_PX = 380; 

    useLayoutEffect(() => {
        // After the component renders, check if the content's full height
        // is greater than our desired max height.
        if (contentRef.current && contentRef.current.scrollHeight > MAX_HEIGHT_PX) {
            // If it is, we know we need to show the "Show More" button.
            setShowButton(true);
        } else {
            setShowButton(false);
        }
    }, [program]);

    const hasDetail = program.detail && program.detail.length > 0;
    const hasFeatures = program.features && program.features.split("|").filter(f => f.trim() !== '').length > 0;
    const hasCommissionDetail = program.commission_detail && program.commission_detail.length > 0;

    return (
        <div className="text-xl md:text-xl h-full">
            <div className="relative">
                <div 
                    ref={contentRef}
                    className="transition-all duration-300 ease-in-out overflow-hidden"
                    style={{ maxHeight: !isExpanded && showButton ? `${MAX_HEIGHT_PX}px` : 'none' }}
                >
                    {hasDetail && <Description detail={program.detail} />}
                    {hasFeatures && <KeyFeatures features={program.features.split("|").filter(f => f.trim() !== '')} />}
                    {hasCommissionDetail && <CommissionStructure commission_detail={program.commission_detail} />}
                </div>
                {!isExpanded && showButton && (
                    <div className="absolute bottom-0 left-0 w-full h-24 bg-gradient-to-t from-white dark:from-background to-transparent pointer-events-none" />
                )}
            </div>
            
            {showButton && (
                <div className="pt-4 text-center">
                    <button
                        onClick={() => setIsExpanded(!isExpanded)}
                        className="inline-flex items-center gap-1 rounded-full border border-slate-300 dark:border-slate-700 bg-transparent px-3 py-1 text-xs font-medium text-slate-600 dark:text-slate-300 transition-colors hover:bg-slate-100 dark:hover:bg-slate-800"
                    >
                        <span>{isExpanded ? "Show Less" : "Show More"}</span>
                        {isExpanded ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
                    </button>
                </div>
            )}
        </div>
    );
}

// Custom BlocksRenderer with configured renderers
function CustomBlocksRenderer({ content }: { content: any }) {
  if (!content) return null;
  
  return (
    <BlocksRenderer 
      content={content}
      blocks={{
        heading: ({ level, children }) => {
          switch(level) {
            case 1:
              return <h1 className="text-xl font-bold my-3 text-slate-800 dark:text-white">{children}</h1>;
            case 2:
              return <h2 className="text-lg font-bold my-2 text-slate-800 dark:text-white">{children}</h2>;
            case 3:
              return <h3 className="text-md font-bold my-2 text-slate-800 dark:text-white">{children}</h3>;
            default:
              return <h4 className="text-sm font-bold my-1 text-slate-800 dark:text-white">{children}</h4>;
          }
        },
        paragraph: ({ children }) => <p className="text-slate-600 dark:text-slate-300 mb-2 text-sm">{children}</p>,
        list: ({ children }) => <ul className="list-disc list-inside space-y-1 my-2 text-slate-600 dark:text-slate-300 text-sm">{children}</ul>,
      }}
    />
  );
}

function Description({ detail }: { detail: any }) {
  return (
    <div className="mb-8">
      <h2 className="text-lg md:text-xl font-bold mb-4 text-slate-800 dark:text-white">Product Detail</h2>
      <div className="text-sm">
        <CustomBlocksRenderer content={detail} />
      </div>
    </div>
  );
}

function KeyFeatures({ features }: { features: string[] }) {
  return (
    <div className="mb-8">
      <h2 className="text-lg md:text-xl font-bold mb-4 text-slate-800 dark:text-white">Key Features</h2>
      <ul className="space-y-2 text-sm text-slate-500 dark:text-slate-300">
        {features.map((feature, i) => (
          <li key={i} className="flex items-center gap-2">
            <CheckIcon />
            <span>{feature}</span>
          </li>
        ))}
      </ul>
    </div>
  );
}

function CommissionStructure({
  commission_detail,
}: {
  commission_detail: any;
}) {
  if (!commission_detail) return null;
  
  return (
    <div className="mb-8">
      <h2 className="text-lg md:text-xl font-bold mb-4 text-slate-800 dark:text-white">Commission Structure</h2>
      <div className="w-full text-sm">
        <CustomBlocksRenderer content={commission_detail} />
      </div>
    </div>
  );
}
