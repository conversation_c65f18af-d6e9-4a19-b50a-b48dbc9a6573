import React from "react";
import { UserPlus, Share2, CreditCard } from "lucide-react";
import { motion } from "framer-motion";

const Steps: React.FC = () => {
  const steps = [
    {
      number: 1,
      title: "Sign Up",
      description:
        "Create your affiliate account in minutes and get approved to start earning.",
      icon: <UserPlus className="w-6 h-6" />,
      color: "bg-blue-500",
    },
    {
      number: 2,
      title: "Start Campaign, Share Your Links, Start Earning",
      description:
        "Share your unique referral links on your website, social media, or email. Every time someone signs up or makes a purchase through your link, you earn a commission.",
      icon: <Share2 className="w-6 h-6" />,
      color: "bg-purple-500",
    },
    {
      number: 3,
      title: "Receive Payment",
      description:
        "Get paid bi-weekly via your preferred payment method. Track your earnings in real-time through your affiliate dashboard.",
      icon: <CreditCard className="w-6 h-6" />,
      color: "bg-green-500",
    },
  ];

  return (
    <section className="pt-10">
      <h2 className="text-2xl md:text-3xl font-bold text-center mb-12 relative">
        <span className="relative z-10">How It Works</span>
        <span className="absolute bottom-0 left-1/2 transform -translate-x-1/2 h-3 w-24 bg-primary/20 -z-0 rounded-full"></span>
      </h2>

      <div className="relative">
        {/* Vertical line connecting the steps */}
        <div className="absolute left-[28px] top-8 bottom-8 w-1 bg-gradient-to-b from-blue-500 via-purple-500 to-green-500 hidden md:block"></div>

        <div className="space-y-12 relative">
          {steps.map((step, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="flex flex-col md:flex-row gap-6 items-start"
            >
              <div
                className={`w-14 h-14 rounded-full ${step.color} text-white flex items-center justify-center flex-shrink-0 shadow-lg z-10`}
              >
                {step.icon}
              </div>
              <div className="bg-card p-6 md:p-8 rounded-xl shadow-md hover:shadow-lg transition-shadow flex-1 border border-muted">
                <div className="flex items-center mb-4">
                  <span className={`w-8 h-8 rounded-full ${step.color} text-white flex items-center justify-center mr-3 font-bold shadow-sm ring-2 ring-white/30`}>
                    {step.number}
                  </span>
                  <h3 className="text-xl font-semibold">{step.title}</h3>
                </div>
                <p className="text-muted-foreground">{step.description}</p>

                {index < steps.length - 1 && (
                  <div className="w-4 h-4 border-r-2 border-b-2 border-muted transform rotate-45 mx-auto mt-6 mb-0 md:hidden"></div>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Steps;
