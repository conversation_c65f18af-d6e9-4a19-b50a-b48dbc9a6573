import React, { useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useToast } from '@/context/ToastContext';
import { actions as authActions } from '@/features/auth/auth.slice';
import { actions as affiliateActions } from '@/features/affiliate/affiliate.slice';
import { actions as socialListeningActions } from '@/features/social-listening/social-listening.slice';
import { RootState } from '@/store';
import { redirectToAuth } from '@/utils/error-handler';
import { Button } from '@/components/ui/button';

const ToastObserver: React.FC = () => {
  const dispatch = useDispatch();
  const { showToast } = useToast();
  
  const authError = useSelector((state: RootState) => state.auth.error);
  const requireAuth = useSelector((state: RootState) => state.auth.requireAuth);
  const requireUpgrade = useSelector((state: RootState) => state.auth.requireUpgrade);
  const affiliateError = useSelector((state: RootState) => state.affiliate.error);
  const successMessage = useSelector((state: RootState) => state.auth.successMessage);
  const transcriptError = useSelector((state: RootState) => state.socialListening.error);
  
  // Handler for showing auth toasts with sign in button
  const showAuthToast = useCallback((message: string) => {
    const toastId = showToast(
      'Authentication Required', 
      message, 
      'warning',
      Infinity, // Don't auto-dismiss
      <Button 
        variant="outline" 
        size="sm" 
        onClick={() => {
          redirectToAuth();
        }}
      >
        Sign In
      </Button>
    );
    
    return toastId;
  }, [showToast]);

  // Handler for showing upgrade toasts
  const showUpgradeToast = useCallback((message: string) => {
    showToast(
      'Upgrade Required', 
      message, 
      'default',
      Infinity, // Don't auto-dismiss
      <Button 
        variant="outline" 
        size="sm" 
        onClick={() => {
          window.location.href = '/profile/upgrade';
        }}
      >
        Upgrade
      </Button>
    );
  }, [showToast]);

  // Handle regular auth errors (no action button)
  useEffect(() => {
    if (authError) {
      showToast(
        'Authentication Error',
        authError,
        'destructive'
      );
      dispatch(authActions.clearMessages());
    }
  }, [authError, showToast, dispatch]);

  // Handle auth required errors (with Sign In button)
  useEffect(() => {
    if (requireAuth) {
      showAuthToast(requireAuth);
      dispatch(authActions.clearMessages());
    }
  }, [requireAuth, showAuthToast, dispatch]);

  // Handle upgrade required messages
  useEffect(() => {
    if (requireUpgrade) {
      showUpgradeToast(requireUpgrade);
      dispatch(authActions.clearMessages());
    }
  }, [requireUpgrade, showUpgradeToast, dispatch]);

  useEffect(() => {
    if (affiliateError) {
      showToast(
        'Affiliate Error',
        affiliateError,
        'destructive'
      );
      dispatch(affiliateActions.setError(null));
    }
  }, [affiliateError, showToast, dispatch]);

  useEffect(() => {
    if (transcriptError) {
      showToast(
        'Transcript Information',
        transcriptError,
        'default'
      );
      dispatch(socialListeningActions.setError(null));
    }
  }, [transcriptError, showToast, dispatch]);
  
  useEffect(() => {
    if (successMessage) {
      showToast(
        'Success',
        successMessage,
        'success'
      );
      dispatch(authActions.clearMessages());
    }
  }, [successMessage, showToast, dispatch]);
  
  return null; // This component doesn't render anything
};

export default ToastObserver;
