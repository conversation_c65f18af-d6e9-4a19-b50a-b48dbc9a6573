{"family": "ecs-task", "containerDefinitions": [{"name": "affiliate", "image": "779037175265.dkr.ecr.us-east-1.amazonaws.com/affiliate:latest", "cpu": 0, "portMappings": [{"name": "port-fargate-1337", "containerPort": 1337, "hostPort": 1337, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [], "environmentFiles": [{"value": "arn:aws:s3:::affiliate-ecs-service-app-configuration/prod.env", "type": "s3"}], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/affiliate-prod-ecs-cluster-task", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "systemControls": []}], "executionRoleArn": "arn:aws:iam::779037175265:role/prod-ecs-task-execution-role", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "3072", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}}