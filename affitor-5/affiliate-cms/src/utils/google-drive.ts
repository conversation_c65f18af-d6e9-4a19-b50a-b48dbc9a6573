// import { google } from 'googleapis';
// import fs from 'fs';
// import path from 'path';

// interface GoogleDriveConfig {
//   type: string;
//   project_id: string;
//   private_key_id: string;
//   private_key: string;
//   client_email: string;
//   client_id: string;
//   auth_uri: string;
//   token_uri: string;
//   auth_provider_x509_cert_url: string;
//   client_x509_cert_url: string;
// }

// class GoogleDriveService {
//   private drive: any;
//   private auth: any;

//   constructor() {
//     this.initializeAuth();
//   }

//   private initializeAuth() {
//     try {
//       // Get credentials from environment variables
//       const credentials: GoogleDriveConfig = {
//         type: process.env.GOOGLE_DRIVE_TYPE || 'service_account',
//         project_id: process.env.GOOGLE_DRIVE_PROJECT_ID || '',
//         private_key_id: process.env.GOOGLE_DRIVE_PRIVATE_KEY_ID || '',
//         private_key: (process.env.GOOGLE_DRIVE_PRIVATE_KEY || '').replace(/\\n/g, '\n'),
//         client_email: process.env.GOOGLE_DRIVE_CLIENT_EMAIL || '',
//         client_id: process.env.GOOGLE_DRIVE_CLIENT_ID || '',
//         auth_uri: process.env.GOOGLE_DRIVE_AUTH_URI || 'https://accounts.google.com/o/oauth2/auth',
//         token_uri: process.env.GOOGLE_DRIVE_TOKEN_URI || 'https://oauth2.googleapis.com/token',
//         auth_provider_x509_cert_url:
//           process.env.GOOGLE_DRIVE_AUTH_PROVIDER_X509_CERT_URL ||
//           'https://www.googleapis.com/oauth2/v1/certs',
//         client_x509_cert_url: process.env.GOOGLE_DRIVE_CLIENT_X509_CERT_URL || '',
//       };

//       // Validate required credentials
//       if (!credentials.client_email || !credentials.private_key || !credentials.project_id) {
//         throw new Error(
//           'Missing required Google Drive credentials. Please check environment variables.'
//         );
//       }

//       // Create JWT auth client
//       this.auth = new google.auth.JWT({
//         email: credentials.client_email,
//         key: credentials.private_key,
//         scopes: ['https://www.googleapis.com/auth/drive.file'],
//       });

//       // Initialize Drive API
//       this.drive = google.drive({ version: 'v3', auth: this.auth });

//       console.log('Google Drive service initialized successfully');
//     } catch (error) {
//       console.error('Failed to initialize Google Drive service:', error);
//       throw error;
//     }
//   }

//   /**
//    * Upload a file to Google Drive
//    * @param filePath - Local path to the file to upload
//    * @param fileName - Name for the file in Google Drive
//    * @param folderId - Optional folder ID to upload to
//    * @returns Promise with file information including public link
//    */
//   async uploadFile(
//     filePath: string,
//     fileName: string,
//     folderId?: string
//   ): Promise<{
//     fileId: string;
//     fileName: string;
//     publicLink: string;
//     webViewLink: string;
//   }> {
//     try {
//       // Check if file exists
//       if (!fs.existsSync(filePath)) {
//         throw new Error(`File not found: ${filePath}`);
//       }

//       // Get file stats for validation
//       const stats = fs.statSync(filePath);
//       console.log(`File size: ${stats.size} bytes`);

//       // Determine MIME type based on file extension
//       const ext = path.extname(fileName).toLowerCase();
//       let mimeType = 'application/octet-stream';

//       switch (ext) {
//         case '.jpg':
//         case '.jpeg':
//           mimeType = 'image/jpeg';
//           break;
//         case '.png':
//           mimeType = 'image/png';
//           break;
//         case '.gif':
//           mimeType = 'image/gif';
//           break;
//         case '.webp':
//           mimeType = 'image/webp';
//           break;
//         case '.bmp':
//           mimeType = 'image/bmp';
//           break;
//         default:
//           mimeType = 'application/octet-stream';
//       }

//       // Prepare file metadata
//       const fileMetadata: any = {
//         name: fileName,
//       };

//       // Add parent folder if specified
//       if (folderId) {
//         fileMetadata.parents = [folderId];
//       }

//       // Upload file
//       const response = await this.drive.files.create({
//         requestBody: fileMetadata,
//         media: {
//           mimeType: mimeType,
//           body: fs.createReadStream(filePath),
//         },
//         fields: 'id,name,webViewLink',
//       });

//       const fileId = response.data.id;
//       const webViewLink = response.data.webViewLink;

//       // Make file publicly accessible
//       await this.drive.permissions.create({
//         fileId: fileId,
//         requestBody: {
//           role: 'reader',
//           type: 'anyone',
//         },
//       });

//       // Generate public direct link
//       const publicLink = `https://drive.google.com/uc?id=${fileId}`;

//       console.log(`File uploaded successfully: ${fileName} (${fileId})`);

//       return {
//         fileId,
//         fileName,
//         publicLink,
//         webViewLink,
//       };
//     } catch (error) {
//       console.error('Error uploading file to Google Drive:', error);
//       throw error;
//     }
//   }

//   /**
//    * Create a folder in Google Drive
//    * @param folderName - Name of the folder to create
//    * @param parentFolderId - Optional parent folder ID
//    * @returns Promise with folder information
//    */
//   async createFolder(
//     folderName: string,
//     parentFolderId?: string
//   ): Promise<{
//     folderId: string;
//     folderName: string;
//   }> {
//     try {
//       const fileMetadata: any = {
//         name: folderName,
//         mimeType: 'application/vnd.google-apps.folder',
//       };

//       if (parentFolderId) {
//         fileMetadata.parents = [parentFolderId];
//       }

//       const response = await this.drive.files.create({
//         requestBody: fileMetadata,
//         fields: 'id,name',
//       });

//       console.log(`Folder created successfully: ${folderName} (${response.data.id})`);

//       return {
//         folderId: response.data.id,
//         folderName: response.data.name,
//       };
//     } catch (error) {
//       console.error('Error creating folder in Google Drive:', error);
//       throw error;
//     }
//   }

//   /**
//    * Delete a file from Google Drive
//    * @param fileId - ID of the file to delete
//    */
//   async deleteFile(fileId: string): Promise<void> {
//     try {
//       await this.drive.files.delete({
//         fileId: fileId,
//       });

//       console.log(`File deleted successfully: ${fileId}`);
//     } catch (error) {
//       console.error('Error deleting file from Google Drive:', error);
//       throw error;
//     }
//   }
// }

// // Export singleton instance
// export const googleDriveService = new GoogleDriveService();
// export default googleDriveService;
