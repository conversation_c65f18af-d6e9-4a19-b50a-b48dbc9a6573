// Import Yoopta Editor types from shared definitions
import type { YooptaContentValue, YooptaBlock } from '../../../../types/yoopta-editor';

export interface IPage {
  id: number;
  documentId: string;
  title: string;
  slug: string;
  content?: YooptaContentValue; // JSON content from Yoopta Editor
  content_html?: string; // HTML representation for SEO
  content_plain?: string; // Plain text for search
  excerpt?: string;
  featured_image?: any;
  meta_title?: string;
  meta_description?: string;
  status: 'draft' | 'published' | 'archived';
  view_count: number;
  author: any; // User relation
  tags?: string[];
  last_edited_at?: string;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  // Referrer-link relationship
  referrer_link?: {
    id: number;
    documentId: string;
    name: string;
    url: string;
    short_link?: string;
    visitors?: number;
    direct_page_views?: number;
    referrer_link_views?: number;
    short_link_views?: number;
    referrer_sources?: Record<string, number>;
  };
}

export interface ICreatePageData {
  title: string;
  content?: YooptaContentValue;
  content_html?: string;
  content_plain?: string;
  excerpt?: string;
  featured_image?: number;
  meta_title?: string;
  meta_description?: string;
  tags?: string[];
}

export interface IUpdatePageData {
  title?: string;
  content?: YooptaContentValue;
  content_html?: string;
  content_plain?: string;
  excerpt?: string;
  featured_image?: number;
  meta_title?: string;
  meta_description?: string;
  status?: 'draft' | 'published' | 'archived';
  tags?: string[];
}

export interface IPageFilters {
  status?: 'draft' | 'published' | 'archived';
  title?: string;
  tags?: string[];
  createdAt?: {
    $gte?: string;
    $lte?: string;
  };
}

export interface IPagePagination {
  page?: number;
  pageSize?: number;
  start?: number;
  limit?: number;
}

export interface IPageResponse {
  data: IPage;
  meta?: any;
}

export interface IPagesResponse {
  data: IPage[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}
