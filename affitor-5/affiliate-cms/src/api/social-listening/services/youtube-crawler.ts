/* eslint-disable no-useless-escape */
'use strict';

import puppeteer from 'puppeteer';

interface YouTubeVideoData {
  video_id: string;
  title: string;
  description: string;
  channel_id: string;
  channel_title: string;
  channel_avatar: string;
  published_from: string;
  thumbnail: string;
  duration: string;
  video_link: string;
  views: number;
  likes: number;
  comments: number;
  shares: number;
  type: string;
  platform: string;
  is_displayed: boolean;
  is_verified: boolean;
  is_from_crawler: boolean;
}

class YouTubeCrawler {
  private browser: any = null;
  private page: any = null;

  async init() {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu',
        ],
      });
    }

    if (!this.page) {
      this.page = await this.browser.newPage();

      // Set user agent to avoid detection
      await this.page.setUserAgent(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      );

      // Set viewport
      await this.page.setViewport({ width: 1366, height: 768 });
    }
  }

  async crawlYouTubeSearch(keyword: string): Promise<YouTubeVideoData[]> {
    try {
      await this.init();

      // Navigate to YouTube search page
      const searchUrl = `https://www.youtube.com/results?search_query=${encodeURIComponent(keyword)}`;
      console.log(`Navigating to YouTube search: ${searchUrl}`);

      await this.page.goto(searchUrl, {
        waitUntil: 'networkidle2',
        timeout: 30000,
      });

      // Wait for video items to load
      await this.page.waitForSelector('ytd-video-renderer, ytd-rich-grid-video-renderer', {
        timeout: 15000,
      });

      // Scroll down to load more content
      await this.autoScroll();

      // Extract video data using a string-based function to avoid TypeScript DOM issues
      const videos = await this.page.evaluate(`
        (() => {
          const results = [];
          // Combined selector for both desktop and mobile layouts
          const videoElements = document.querySelectorAll('ytd-video-renderer, ytd-rich-grid-video-renderer');
          
          videoElements.forEach((element) => {
            try {
              // Extract video link and ID
              const linkElement = element.querySelector('a#thumbnail[href*="/watch?"]');
              if (!linkElement) return;

              const videoLink = linkElement.href;
              const videoIdMatch = videoLink.match(/[?&]v=([^&]+)/);
              if (!videoIdMatch) return;

              const videoId = videoIdMatch[1];

              // Extract video title
              const titleElement = element.querySelector('#video-title, .title-wrapper h3');
              const titleText = titleElement?.textContent?.trim() || '';
              
              // Extract channel information
              const channelElement = element.querySelector('a.yt-simple-endpoint[href*="/channel/"], a.yt-simple-endpoint[href*="/@"]');
              const channelTitle = channelElement?.textContent?.trim() || '';
              const channelLink = channelElement?.href || '';
              
              // Extract channel ID from link
              let channelId = '';
              if (channelLink) {
                const channelMatch = channelLink.match(/\\/channel\\/([^/]+)/);
                if (channelMatch) {
                  channelId = channelMatch[1];
                } else {
                  // Use handle as ID for new-style @username channels
                  const handleMatch = channelLink.match(/\\/@([^/]+)/);
                  if (handleMatch) {
                    channelId = handleMatch[1];
                  }
                }
              }
              
              // Extract channel avatar
              const avatarElement = element.querySelector('#avatar-link img, #channel-thumbnail img');
              let channelAvatar = avatarElement?.src || '';
              
              // Clean up avatar URL (sometimes it contains 2x or other resolution markers)
              if (channelAvatar && channelAvatar.includes('=s')) {
                channelAvatar = channelAvatar.split('=s')[0] + '=s176-c-k-c0x00ffffff-no-rj';
              }
              
              // Extract video thumbnail
              const thumbnailImg = element.querySelector('#thumbnail img');
              let thumbnail = thumbnailImg?.src || '';
              
              // Sometimes thumbnails use data-src instead of src
              if (!thumbnail || thumbnail.includes('placeholder')) {
                thumbnail = thumbnailImg?.getAttribute('data-src') || '';
              }
              
              // Extract video duration
              const durationElement = element.querySelector('.ytd-thumbnail-overlay-time-status-renderer, #text.ytd-thumbnail-overlay-time-status-renderer');
              const durationText = durationElement?.textContent?.trim() || '0:00';
              
              // Extract published date/time
              const metadataLine = element.querySelector('#metadata-line');
              let publishedTimeText = '';
              let viewsText = '0';
              
              if (metadataLine) {
                const spans = metadataLine.querySelectorAll('span');
                spans.forEach(span => {
                  const text = span.textContent?.trim() || '';
                  if (text.includes('view')) {
                    viewsText = text;
                  } else if (text.includes('ago') || text.includes('hour') || text.includes('day') || text.includes('week') || text.includes('month') || text.includes('year')) {
                    publishedTimeText = text;
                  }
                });
              }
              
              // Parse views
              const parseViews = (viewsText) => {
                if (!viewsText) return 0;
                
                const cleanText = viewsText.replace(/[^\\d.KMB]/gi, '');
                
                if (cleanText.includes('K')) {
                  return Math.floor(parseFloat(cleanText.replace('K', '')) * 1000);
                } else if (cleanText.includes('M')) {
                  return Math.floor(parseFloat(cleanText.replace('M', '')) * 1000000);
                } else if (cleanText.includes('B')) {
                  return Math.floor(parseFloat(cleanText.replace('B', '')) * 1000000000);
                }
                
                return parseInt(cleanText) || 0;
              };
              
              // Parse published time to approximate date
              const parseTimeText = (timeText) => {
                if (!timeText) return new Date().toISOString();
                
                const now = new Date();
                
                if (timeText.includes('hour')) {
                  const hours = parseInt(timeText) || 1;
                  now.setHours(now.getHours() - hours);
                } else if (timeText.includes('day')) {
                  const days = parseInt(timeText) || 1;
                  now.setDate(now.getDate() - days);
                } else if (timeText.includes('week')) {
                  const weeks = parseInt(timeText) || 1;
                  now.setDate(now.getDate() - (weeks * 7));
                } else if (timeText.includes('month')) {
                  const months = parseInt(timeText) || 1;
                  now.setMonth(now.getMonth() - months);
                } else if (timeText.includes('year')) {
                  const years = parseInt(timeText) || 1;
                  now.setFullYear(now.getFullYear() - years);
                }
                
                return now.toISOString();
              };

              const views = parseViews(viewsText);
              const publishedFrom = parseTimeText(publishedTimeText);

              // Extract description if available (not always present in search results)
              const descriptionElement = element.querySelector('#description-text, .description-text');
              const description = descriptionElement?.textContent?.trim() || '';

              results.push({
                video_id: videoId,
                title: titleText.slice(0, 200), // Limit title length
                description: description,
                channel_id: channelId,
                channel_title: channelTitle,
                channel_avatar: channelAvatar,
                published_from: publishedFrom,
                thumbnail: thumbnail,
                duration: durationText,
                video_link: videoLink,
                views: views,
                likes: 0, // Not available in search results
                comments: 0, // Not available in search results
                shares: 0, // Not available in search results
                type: 'video',
                platform: 'youtube',
                is_displayed: false,
                is_verified: false,
                is_from_crawler: true, // Mark as from crawler
              });
            } catch (error) {
              console.error('Error extracting YouTube video data:', error);
            }
          });

          // Filter out ads by checking for standard video data
          return results.filter(video => video.video_id && video.title && video.channel_title);
        })()
      `);

      console.log(`Extracted ${videos.length} videos from YouTube search`);
      return videos;
    } catch (error) {
      console.error('Error crawling YouTube search:', error);
      throw error;
    }
  }

  private async autoScroll() {
    try {
      await this.page.evaluate(`
        (async () => {
          await new Promise((resolve) => {
            let totalHeight = 0;
            const distance = 100;
            const maxScrolls = 20; // Limit scrolling to avoid infinite scroll
            let scrolls = 0;
            const timer = setInterval(() => {
              const scrollHeight = document.body.scrollHeight;
              window.scrollBy(0, distance);
              totalHeight += distance;
              scrolls++;

              if (totalHeight >= scrollHeight || scrolls >= maxScrolls) {
                clearInterval(timer);
                resolve();
              }
            }, 100);
          });
        })()
      `);

      // Wait for new content to load
      await new Promise((resolve) => setTimeout(resolve, 2000));
    } catch (error) {
      console.error('Error during auto scroll:', error);
    }
  }

  async close() {
    if (this.page) {
      await this.page.close();
      this.page = null;
    }

    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  /**
   * Gets detailed video statistics by visiting the video page directly
   * @param videoId YouTube video ID
   * @returns Object containing engagement metrics (likes, comments, views, etc.)
   */
  async getVideoDetailStats(videoId: string): Promise<{
    views: number;
    likes: number;
    comments: number;
    published: string;
    description: string;
  }> {
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        await this.init();

        // Create a new page for each request to avoid frame detachment issues
        const newPage = await this.browser.newPage();

        // Set user agent and viewport for the new page
        await newPage.setUserAgent(
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        );
        await newPage.setViewport({ width: 1366, height: 768 });

        // Construct video URL from ID
        const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;
        console.log(`Getting detailed stats for YouTube video: ${videoUrl}`);

        await newPage.goto(videoUrl, {
          waitUntil: 'domcontentloaded',
          timeout: 20000,
        });

        // Wait for content to load with a shorter timeout
        await newPage.waitForSelector('#info-contents, #primary-inner', { timeout: 10000 });

        // Extract data using client-side JavaScript
        const stats = await newPage.evaluate(`
          (async () => {
            // Helper function to parse view count text
            function parseViewCount(text = '') {
              if (!text) return 0;
              
              const numStr = text.replace(/[^0-9]/g, '');
              return numStr ? parseInt(numStr, 10) : 0;
            }
            
            // Helper function to parse like count text which might have K, M, B suffix
            function parseLikeCount(text = '') {
              if (!text) return 0;
              
              const cleanText = text.replace(/[^\\d.KkMmBb]/gi, '');
              
              if (/[Kk]$/.test(cleanText)) {
                return Math.floor(parseFloat(cleanText.replace(/[Kk]/i, '')) * 1000);
              } else if (/[Mm]$/.test(cleanText)) {
                return Math.floor(parseFloat(cleanText.replace(/[Mm]/i, '')) * 1000000);
              } else if (/[Bb]$/.test(cleanText)) {
                return Math.floor(parseFloat(cleanText.replace(/[Bb]/i, '')) * 1000000000);
              }
              
              return parseInt(cleanText, 10) || 0;
            }

            // Helper function to parse comment count
            function parseCommentCount(text = '') {
              if (!text) return 0;
              
              // Extract number from strings like "1,234 comments" or "1.2K comments"
              const match = text.match(/([\d,]+|[\d.]+[KkMmBb])/i);
              if (match) {
                return parseLikeCount(match[1]); 
              }
              
              return 0;
            }

            // Extract view count - try multiple selectors
            let viewCount = 0;
            const viewSelectors = [
              '#info-contents #count .view-count',
              '#info #count .view-count',
              '.view-count',
              '[class*="view-count"]',
              '#info-contents span:contains("views")',
              '#primary-inner span:contains("views")'
            ];
            
            for (const selector of viewSelectors) {
              try {
                const element = document.querySelector(selector);
                if (element && element.textContent) {
                  viewCount = parseViewCount(element.textContent);
                  if (viewCount > 0) break;
                }
              } catch (e) {
                continue;
              }
            }

            // Extract like count - try multiple selectors
            let likeCount = 0;
            const likeSelectors = [
              '#segmented-like-button button[aria-label]',
              '#top-level-buttons-computed ytd-toggle-button-renderer:first-child #text',
              '[aria-label*="like"]',
              '.ytd-toggle-button-renderer #text',
              'button[aria-label*="like"] #text'
            ];
            
            for (const selector of likeSelectors) {
              try {
                const element = document.querySelector(selector);
                if (element) {
                  const text = element.getAttribute('aria-label') || element.textContent || '';
                  likeCount = parseLikeCount(text);
                  if (likeCount > 0) break;
                }
              } catch (e) {
                continue;
              }
            }

            // Extract comment count - try multiple selectors
            let commentCount = 0;
            const commentSelectors = [
              '#comments #count',
              '#comments-header #count',
              '#comments span:contains("comment")',
              '[id*="comment"] #count'
            ];
            
            for (const selector of commentSelectors) {
              try {
                const element = document.querySelector(selector);
                if (element && element.textContent) {
                  const text = element.textContent || '';
                  if (text.includes('comment')) {
                    commentCount = parseCommentCount(text);
                    if (commentCount > 0) break;
                  }
                }
              } catch (e) {
                continue;
              }
            }
            
            // Extract publish date - try multiple selectors
            let publishedDate = '';
            const dateSelectors = [
              '#info-contents #info-text #date yt-formatted-string',
              '#info #date yt-formatted-string',
              '#date yt-formatted-string',
              '[id*="date"] yt-formatted-string',
              '#primary-inner #date'
            ];
            
            for (const selector of dateSelectors) {
              try {
                const element = document.querySelector(selector);
                if (element && element.textContent) {
                  publishedDate = element.textContent.trim();
                  if (publishedDate) break;
                }
              } catch (e) {
                continue;
              }
            }
            
            // Extract description - try multiple selectors
            let description = '';
            const descSelectors = [
              '#description-inline-expander #description',
              '#description-text',
              '#primary-inner #description',
              '[id*="description"]'
            ];
            
            for (const selector of descSelectors) {
              try {
                const element = document.querySelector(selector);
                if (element && element.textContent) {
                  description = element.textContent.trim();
                  if (description.length > 50) break; // Only use if it's substantial
                }
              } catch (e) {
                continue;
              }
            }

            return {
              views: viewCount,
              likes: likeCount,
              comments: commentCount,
              published: publishedDate,
              description: description
            };
          })()
        `);

        // Close the new page
        await newPage.close();

        console.log(`Successfully extracted stats for video ${videoId}:`, stats);
        return stats;
      } catch (error) {
        retryCount++;
        console.error(
          `Error getting detailed stats for video ${videoId} (attempt ${retryCount}/${maxRetries}):`,
          error.message
        );

        // Close any open pages to prevent memory leaks
        try {
          const pages = await this.browser.pages();
          for (const page of pages) {
            if (!page.isClosed()) {
              await page.close();
            }
          }
        } catch (cleanupError) {
          console.error('Error during page cleanup:', cleanupError.message);
        }

        if (retryCount >= maxRetries) {
          console.error(`Max retries reached for video ${videoId}, returning default stats`);
          // Return default empty stats after max retries
          return {
            views: 0,
            likes: 0,
            comments: 0,
            published: '',
            description: '',
          };
        }

        // Wait before retry
        await new Promise((resolve) => setTimeout(resolve, 2000 * retryCount));
      }
    }
  }

  /**
   * Enhancement to crawlYouTubeSearch that adds detailed engagement statistics
   * for the top N results by visiting each video page
   */
  async crawlYouTubeSearchWithDetailedStats(
    keyword: string,
    detailLimit: number = 10
  ): Promise<YouTubeVideoData[]> {
    try {
      // First get basic search results
      const videos = await this.crawlYouTubeSearch(keyword);
      console.log(
        `Found ${videos.length} videos in search, getting detailed stats for top ${detailLimit}`
      );

      // Get detailed stats for the first N videos (to avoid too many requests)
      const videosToEnhance = videos.slice(0, detailLimit);

      // Process videos sequentially to avoid overloading and frame detachment issues
      for (const video of videosToEnhance) {
        try {
          console.log(`Getting detailed stats for video ${video.title} (${video.video_id})`);
          const stats = await this.getVideoDetailStats(video.video_id);

          // Update video with accurate stats from the detail page
          if (stats.views > 0) video.views = stats.views;
          if (stats.likes > 0) video.likes = stats.likes;
          if (stats.comments > 0) video.comments = stats.comments;

          // If we got a better description, use it
          if (stats.description && stats.description.length > video.description.length) {
            video.description = stats.description;
          }

          // If we have a more specific published date, parse and use it
          if (stats.published) {
            try {
              // Try to parse the date (e.g., "Premiered Oct 10, 2022" or "3 years ago")
              const now = new Date();

              // Check for patterns like "X years/months/days ago"
              const agoMatch = stats.published.match(
                /(\d+)\s+(year|month|day|hour|minute|second)s?\s+ago/i
              );
              if (agoMatch) {
                const value = parseInt(agoMatch[1]);
                const unit = agoMatch[2].toLowerCase();

                if (unit === 'year') now.setFullYear(now.getFullYear() - value);
                else if (unit === 'month') now.setMonth(now.getMonth() - value);
                else if (unit === 'day') now.setDate(now.getDate() - value);
                else if (unit === 'hour') now.setHours(now.getHours() - value);
                else if (unit === 'minute') now.setMinutes(now.getMinutes() - value);
                else if (unit === 'second') now.setSeconds(now.getSeconds() - value);

                video.published_from = now.toISOString();
              }
              // Check for specific date formats
              else if (stats.published.includes(',')) {
                // Format like "Oct 10, 2022"
                const parsedDate = new Date(stats.published);
                if (!isNaN(parsedDate.getTime())) {
                  video.published_from = parsedDate.toISOString();
                }
              }
            } catch (dateError) {
              console.error(
                `Error parsing date "${stats.published}" for video ${video.video_id}:`,
                dateError
              );
            }
          }

          // Longer delay to avoid rate limiting and frame detachment
          await new Promise((resolve) => setTimeout(resolve, 3000));
        } catch (detailError) {
          console.error(`Error enhancing video ${video.video_id}:`, detailError);
          // Continue with the next video
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }

      return videos;
    } catch (error) {
      console.error('Error in crawlYouTubeSearchWithDetailedStats:', error);
      throw error;
    }
  }
}

export default YouTubeCrawler;
