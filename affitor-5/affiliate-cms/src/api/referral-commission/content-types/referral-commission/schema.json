{"kind": "collectionType", "collectionName": "referral_commissions", "info": {"singularName": "referral-commission", "pluralName": "referral-commissions", "displayName": "Referral Commission", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"commission_amount": {"type": "decimal"}, "commission_status": {"type": "enumeration", "enum": ["ready", "pending", "paid"]}, "review_due_date": {"type": "date"}, "payment_date": {"type": "date"}, "referral": {"type": "relation", "relation": "manyToOne", "target": "api::referral.referral", "inversedBy": "referral_commissions"}, "referrer": {"type": "relation", "relation": "manyToOne", "target": "api::referrer.referrer", "inversedBy": "referral_commissions"}, "subscription_tier": {"type": "relation", "relation": "oneToOne", "target": "api::subscription-tier.subscription-tier"}, "gross_sale_amount": {"type": "decimal"}, "commission_percentage": {"type": "decimal"}}}