/**
 * chat-session controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::chat-session.chat-session', ({ strapi }) => ({
  async create(ctx) {
    try {
      const { user } = ctx.state;
      if (!user) {
        return ctx.unauthorized('You must be logged in to create a chat session');
      }

      const session = await strapi.service('api::chat-session.chat-session').createSession(user.id);
      return { data: session };
    } catch (err) {
      console.error('Error in create session controller:', err);
      return ctx.badRequest('Failed to create chat session', { error: err });
    }
  },

  async endSession(ctx) {
    try {
      const { user } = ctx.state;


      if (!user) {
        return ctx.unauthorized('You must be logged in to end chat sessions');
      }

      const result = await strapi
        .service('api::chat-session.chat-session')
        .terminateAllActiveSessions(user.id);

      return {
        data: result.sessions,
        meta: {
          count: result.count,
          message: result.count > 0 ? 'Sessions terminated' : 'No active sessions found',
        },
      };
    } catch (err) {
      console.error('Error in end session controller:', err);
      return ctx.badRequest('Failed to end chat sessions', { error: err });
    }
  },
}));
