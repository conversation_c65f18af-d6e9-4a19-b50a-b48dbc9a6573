{"kind": "collectionType", "collectionName": "payment_methods", "info": {"singularName": "payment-method", "pluralName": "payment-methods", "displayName": "Payment Method"}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string"}, "image": {"allowedTypes": ["images", "files", "videos", "audios"], "type": "media", "multiple": false}, "affiliates": {"type": "relation", "relation": "manyToMany", "target": "api::affiliate.affiliate", "mappedBy": "payment_methods"}}}