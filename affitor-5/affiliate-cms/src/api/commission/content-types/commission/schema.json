{"kind": "collectionType", "collectionName": "commissions", "info": {"singularName": "commission", "pluralName": "commissions", "displayName": "Commission", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"title": {"type": "string"}, "value_from": {"type": "decimal", "default": 0}, "value_to": {"type": "decimal", "default": 0}, "max_percentage": {"type": "decimal"}, "note": {"type": "string"}, "type": {"type": "enumeration", "enum": ["fix_price", "fix_percentage", "range_percentage"]}, "commission_detail": {"type": "blocks"}, "affiliates": {"type": "relation", "relation": "oneToMany", "target": "api::affiliate.affiliate", "mappedBy": "commission"}, "avg_commission": {"type": "decimal"}}}