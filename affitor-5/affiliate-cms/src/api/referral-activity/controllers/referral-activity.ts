/**
 * referral-activity controller
 */

import { factories } from '@strapi/strapi';
import { errors } from '@strapi/utils';

const { ApplicationError, ValidationError } = errors;

export default factories.createCoreController(
  'api::referral-activity.referral-activity',
  ({ strapi }) => ({
    async find(ctx) {
      const user = ctx.state.user;

      if (!user) {
        throw new ApplicationError('Authentication required', { code: 'UNAUTHORIZED' });
      }

      const referrer = await strapi.entityService.findMany('api::referrer.referrer', {
        filters: {
          user: user.id,
        },
        fields: ['id'],
        limit: 1,
      });

      if (!referrer || referrer.length === 0) {
        return super.find(ctx);
      }

      ctx.query = {
        ...ctx.query,
        filters: {
          ...(typeof ctx.query.filters === 'object' && ctx.query.filters !== null
            ? ctx.query.filters
            : {}),
          referral: {
            referrer: referrer[0].id,
          }, 
        },
      };

      // Default behavior for regular find requests
      return super.find(ctx);
    },
  })
);
