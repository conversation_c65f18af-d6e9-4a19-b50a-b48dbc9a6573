/**
 * aiscript-session service
 */

import { factories } from '@strapi/strapi';
import { v4 as uuidv4 } from 'uuid';
import { IAIScriptSession } from '../../aiscript/interfaces';

export default factories.createCoreService(
  'api::aiscript-session.aiscript-session',
  ({ strapi }) => ({
    async getActiveSession(userId) {
      const sessions = await strapi.entityService.findMany(
        'api::aiscript-session.aiscript-session',
        {
          filters: {
            users_permissions_user: userId,
            session_status: 'active',
          },
          populate: ['users_permissions_user'],
          sort: { createdAt: 'desc' },
          limit: 1,
        }
      );

      return sessions && sessions.length > 0 ? sessions[0] : null;
    },
    async getActiveSessionById(sessionId) {
      const sessions = await strapi.entityService.findMany(
        'api::aiscript-session.aiscript-session',
        {
          filters: {
            session_id: sessionId,
            session_status: 'active',
          },
          populate: ['users_permissions_user'],
          sort: { createdAt: 'desc' },
          limit: 1,
        }
      );

      return sessions && sessions.length > 0 ? sessions[0] : null;
    },

    async createSession({ sessionId = uuidv4(), userId }: { sessionId: string; userId: string }) {
      // Create session without input - inputs will be stored in aiscripts
      const session = await strapi.entityService.create('api::aiscript-session.aiscript-session', {
        data: {
          session_id: sessionId,
          start_time: new Date(),
          session_status: 'active',
          users_permissions_user: userId,
        },
      });

      return session;
    },

    async endSession(sessionId) {
      console.log('LOG-endSession for AI script session:', sessionId);
      const sessions = await strapi.entityService.findMany(
        'api::aiscript-session.aiscript-session',
        {
          filters: {
            session_id: sessionId,
          },
        }
      );

      if (!sessions || sessions.length === 0) {
        throw new Error(`Session with ID ${sessionId} not found`);
      }

      const session = sessions[0];

      console.log('LOG-session:', session);

      return strapi.entityService.update('api::aiscript-session.aiscript-session', session.id, {
        data: {
          end_time: new Date(),
          session_status: 'ended',
        },
      });
    },

    async terminateAllActiveSessions(userId: string): Promise<any> {
      try {
        console.log('LOG-terminateAllActiveSessions for AI script sessions:', userId);

        // Get all active sessions for the user
        const activeSessions = await strapi.entityService.findMany(
          'api::aiscript-session.aiscript-session',
          {
            filters: {
              session_status: 'active',
              users_permissions_user: {
                id: userId,
              },
            },
          }
        );

        console.log('LOG-activeSessions:', activeSessions);
        if (!activeSessions || activeSessions.length === 0) {
          return { count: 0, sessions: [] };
        }

        // Get the session IDs
        const sessionIds = activeSessions.map((session) => session.id);

        // Update all sessions
        await strapi.db.query('api::aiscript-session.aiscript-session').updateMany({
          where: {
            id: { $in: sessionIds },
          },
          data: {
            end_time: new Date(),
            session_status: 'ended',
          },
        });

        // Return the updated session IDs and count
        return {
          count: activeSessions.length,
          sessions: activeSessions.map((session) => ({
            ...session,
            id: String(session.id),
            session_status: 'ended',
            end_time: new Date(),
          })),
        };
      } catch (error) {
        console.error('Error terminating all active AI script sessions:', error);
        throw error;
      }
    },
  })
);
