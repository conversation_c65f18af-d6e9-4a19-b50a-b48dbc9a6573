export default [
  'strapi::errors',
  {
    name: 'strapi::security',
    config: {
      contentSecurityPolicy: {
        directives: {
          'img-src': [
            "'self'",
            'data:',
            'blob:',
            'https://market-assets.strapi.io',
            'https://strapi-affiliate.s3.us-east-1.amazonaws.com', // ✅ Add your S3 bucket
          ],
        },
      },
    },
  },

  'strapi::cors',
  'strapi::poweredBy',
  'strapi::logger',
  'strapi::query',
  { name: 'strapi::body', config: { includeUnparsed: true } },
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
  'global::content-manager-transaction',
  'global::user-tracking',
  'global::referral-code',
];
