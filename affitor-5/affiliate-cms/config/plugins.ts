// ~/strapi-aws-s3/backend/config/plugins.ts

module.exports = ({ env }) => {
  const commonConfig = {
    provider: 'aws-s3',
    providerOptions: {
      rootPath: env('ROOT_PATH_UPLOAD', 'strapi'),
      accessKeyId: env('AWS_ACCESS_KEY_ID'),
      secretAccessKey: env('AWS_ACCESS_SECRET'),
      region: env('AWS_REGION', 'us-east-1'),
      params: {
        ACL: env('AWS_ACL', 'public-read'),
        signedUrlExpires: env('AWS_SIGNED_URL_EXPIRES', 15 * 60),
        Bucket: env('AWS_BUCKET'),
      },
    },
    breakpoints: {
      large: 1920,
      medium: 1280,
      small: 720,
    },
    actionOptions: {
      upload: {},
      uploadStream: {},
      delete: {},
    },
  };

  return {
    upload: {
      config: commonConfig,
    },
    'users-permissions': {
      config: {
        jwt: {
          expiresIn: '90d',
        },
      },
    },
  };
};
