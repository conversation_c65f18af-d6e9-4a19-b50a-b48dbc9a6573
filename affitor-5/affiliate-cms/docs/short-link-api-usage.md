# Short Link API Usage Guide

This document describes how to use the new short link functionality for referrer links.

## Overview

The short link feature allows you to:
1. Automatically generate unique short links when creating referrer links
2. Search for referrer links using their short link
3. Track clicks using short links
4. Generate short links for existing referrer links that don't have them

## API Endpoints

### 1. Create Referrer Link (with automatic short link generation)

**POST** `/api/referrer-links`

```json
{
  "data": {
    "url": "https://example.com/product",
    "name": "My Product Link"
  }
}
```

Response includes the generated `short_link` field:
```json
{
  "data": {
    "id": 1,
    "name": "My Product Link",
    "url": "https://example.com/product?via=ABC123",
    "short_link": "Xy9Kp2Mn",
    "visitors": 0,
    "leads": 0,
    "conversions": 0
  }
}
```

### 2. Find Referrer Link by Short Link

**GET** `/api/referrer-links/short/:shortLink`

Example: `GET /api/referrer-links/short/Xy9Kp2Mn`

Response:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "My Product Link",
    "url": "https://example.com/product?via=ABC123",
    "short_link": "Xy9Kp2Mn",
    "visitors": 5,
    "leads": 2,
    "conversions": 1,
    "referrer": {
      "id": 1,
      "referral_code": "ABC123"
    }
  }
}
```

### 3. Track Click by Short Link

**POST** `/api/referrer-links/track-click-short`

```json
{
  "shortLink": "Xy9Kp2Mn"
}
```

Response:
```json
{
  "success": true,
  "message": "Click tracked successfully via short link",
  "data": {
    "linkId": 1,
    "linkName": "My Product Link",
    "visitors": 6,
    "url": "https://example.com/product?via=ABC123",
    "shortLink": "Xy9Kp2Mn"
  }
}
```

### 4. Generate Short Links for Existing Referrer Links

**POST** `/api/referrer-links/generate-short-links`

This endpoint will generate short links for all existing referrer links that don't have them.

Response:
```json
{
  "success": true,
  "message": "Generated short links for 5 referrer links",
  "updated": 5
}
```

## Short Link Format

- Short links are 8 characters long
- Use alphanumeric characters (A-Z, a-z, 0-9)
- Each short link is guaranteed to be unique
- Example: `Xy9Kp2Mn`, `A1b2C3d4`, `ZzYyXxWw`

## Usage Examples

### Frontend Integration

```javascript
// Create a referrer link
const createLink = async (url, name) => {
  const response = await fetch('/api/referrer-links', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      data: { url, name }
    })
  });
  
  const result = await response.json();
  console.log('Short link:', result.data.short_link);
  return result.data;
};

// Find link by short link
const findByShortLink = async (shortLink) => {
  const response = await fetch(`/api/referrer-links/short/${shortLink}`);
  const result = await response.json();
  return result.data;
};

// Track click by short link
const trackClick = async (shortLink) => {
  const response = await fetch('/api/referrer-links/track-click-short', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ shortLink })
  });
  
  const result = await response.json();
  console.log('Click tracked:', result.message);
  return result.data;
};
```

### URL Shortening Service

You can use the short links to create a URL shortening service:

```
Original URL: https://example.com/product?via=ABC123
Short URL: https://yourdomain.com/s/Xy9Kp2Mn
```

When someone visits the short URL, you can:
1. Look up the referrer link using the short link
2. Track the click
3. Redirect to the original URL

## Notes

- Short links are automatically generated when creating new referrer links
- Existing referrer links can be updated with short links using the generate endpoint
- Short links are unique across the entire system
- Click tracking works the same way for both regular URLs and short links
- Authentication is required for most endpoints (except finding by short link)
